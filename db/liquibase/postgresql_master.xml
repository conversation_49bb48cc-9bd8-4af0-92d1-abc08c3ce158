<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <!-- 文件请以版本号命名，每个版本包含2个文件（xml文件中存放存储过程和视图类的，剩下的都放在sql中） -->
    <include file="changelog/postgresql/V1.0.0(250109).sql" relativeToChangelogFile="true"/>
    <include file="changelog/postgresql/V1.0.0(250109).xml" relativeToChangelogFile="true"/>
    <include file="changelog/postgresql/V1.1.0(250506).sql" relativeToChangelogFile="true"/>
    <include file="changelog/postgresql/V1.1.0(250506).xml" relativeToChangelogFile="true"/>
    <include file="changelog/postgresql/V1.2.0(250519).sql" relativeToChangelogFile="true"/>
    <include file="changelog/postgresql/V1.2.0(250519).xml" relativeToChangelogFile="true"/>
    <include file="changelog/postgresql/V1.3.0(250616).sql" relativeToChangelogFile="true"/>
</databaseChangeLog>
