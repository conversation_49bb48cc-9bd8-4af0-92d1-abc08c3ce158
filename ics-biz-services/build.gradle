dependencies {
    compile group: 'org.mybatis', name: 'mybat<PERSON>', version: '3.5.5'
    compile "com.oracle:ojdbc6:11.2.0.1.0"
    compile 'org.apache.commons:commons-lang3:3.8.1'
    compile "com.xdo:xdo-i:3.0.+"
    compile "com.xdo:xdo-bulkinsert:3.0.+"
//    compile "com.xdo:xdo-export:3.0.+"
    compile "com.xdo:xdo-springboot:3.2.+"
    compile "com.xdo:xdo-file:3.1.+"
    compile('com.xdo:xdo-mybatis:3.0.+')
    compile "com.xdo:xdo-interceptor:3.0.+"
    compile 'com.xdo:xdo-find-sword:3.2.+'
    compile "com.xdo:xdo-import:3.41.+"
    compile "com.xdo:xdo-export-async-lib:4.0.+"
    compile "com.xdo:xdo-multidb:3.0.+"
    compile "com.xdo:xdo-audit-lib:3.0.+"
    compile "com.xdo:xdo-audit-dto:3.0.+"
    compile "com.xdo:xdo-common:3.1.+"
    compile "com.alibaba:easyexcel:2.2.6"
    compile "com.alibaba:fastjson:1.2.66"
    compile 'com.squareup.okhttp3:okhttp:3.12.1'
    compile 'com.squareup.retrofit2:retrofit:2.5.0'
    compile 'com.squareup.retrofit2:converter-jackson:2.5.0'
    compile "com.google.zxing:core:3.4.0"//todo 二维码生成
    compile "commons-net:commons-net:3.6"
    compile group: 'org.postgresql', name: 'postgresql', version: '42.2.24'
    compile "org.springframework.kafka:spring-kafka:2.2.8.RELEASE"
    compile 'org.springframework.boot:spring-boot-starter-aop:2.1.1.RELEASE'
    implementation 'com.itextpdf:itextpdf:5.5.13.2'
    implementation 'com.itextpdf:itext-asian:5.2.0'
    implementation group: 'org.apache.commons', name: 'commons-text', version: '1.9'
    implementation('com.yuncheng:yuncheng-framework-workflow:4.0.0') {
        exclude group: 'com.github.jsqlparser', module: 'jsqlparser'
    }
    // 强制使用兼容的JSQLParser版本
    implementation 'com.github.jsqlparser:jsqlparser:1.0'
    compile 'com.sun.mail:javax.mail:1.6.2'
    compile "org.jsoup:jsoup:1.12.1"
    implementation group: 'com.jcraft', name: 'jsch', version: '0.1.55'
    
    // 添加 Apache PDFBox 依赖用于PDF文件合并
    implementation group: 'org.apache.pdfbox', name: 'pdfbox', version: '2.0.27'

    compile fileTree(dir: 'libs', include: ['*.jar'])
    compile("com.dc.swopool-backend:swo-pool-shared:1.+") {
        exclude group: 'com.github.pagehelper', module: 'pagehelper'
    }
    compile 'org.bouncycastle:bcprov-jdk15on:1.59'
    implementation group: 'net.lingala.zip4j', name: 'zip4j', version: '2.11.1'
    implementation group: 'com.dameng', name: 'DmJdbcDriver18', version: '*********'
    implementation 'org.apache.pdfbox:pdfbox:2.0.24'
    implementation('technology.tabula:tabula:1.0.5') {
        exclude group: 'org.slf4j', module: 'slf4j-simple'
    }}
sourceSets.main.resources {
    srcDirs = ['src/main/java']
    include '**/*.xml'
}
