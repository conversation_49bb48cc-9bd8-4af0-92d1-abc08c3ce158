<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIPurchaseListBoxMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizIPurchaseListBox">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="product_grade" property="productGrade" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="qty" property="qty" jdbcType="NUMERIC"/>
        <result column="curr" property="curr" jdbcType="VARCHAR"/>
        <result column="dec_price" property="decPrice" jdbcType="NUMERIC"/>
        <result column="dec_total" property="decTotal" jdbcType="NUMERIC"/>
        <result column="product_type" property="productType" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="discount_rate" property="discountRate" jdbcType="NUMERIC"/>
        <result column="discount_amount" property="discountAmount" jdbcType="NUMERIC"/>
        <result column="payment_amount" property="paymentAmount" jdbcType="NUMERIC"/>
        <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="box_no" property="boxNo" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="NUMERIC"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="data_status" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="list_head_sid" property="listHeadSid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.sid, 
            coalesce(t.update_user,t.insert_user) as insert_user,
            coalesce(t.update_time,t.insert_time) as insert_time,
            coalesce(t.update_user_name,t.insert_user_name) as insert_user_name,
            t.update_user, 
            t.update_time, 
            t.update_user_name, 
            t.trade_code, 
            t.product_grade, 
            t.unit, 
            t.qty, 
            t.curr, 
            t.dec_price, 
            t.dec_total, 
            t.product_type, 
            t.head_id, 
            t.discount_rate, 
            t.discount_amount, 
            t.payment_amount, 
            t.invoice_no, 
            t.box_no, 
            t.quantity, 
            t.version_no, 
            t.data_status, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10,
            t.list_head_sid
    </sql>

    <sql id="condition">
        <if test="sid != null and sid  != ''">
            AND t.sid LIKE '%' || #{sid} || '%'
        </if>
        <if test="insertUser != null and insertUser  != ''">
            AND t.insert_user LIKE '%' || #{insertUser} || '%'
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.insert_time >= to_timestamp(insertTimeFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.insert_time <= to_timestamp(insertTimeTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="insertUserName != null and insertUserName  != ''">
            AND t.insert_user_name LIKE '%' || #{insertUserName} || '%'
        </if>
        <if test="updateUser != null and updateUser  != ''">
            AND t.update_user LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.update_time >= to_timestamp(updateTimeFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.update_time <= to_timestamp(updateTimeTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="updateUserName != null and updateUserName  != ''">
            AND t.update_user_name LIKE '%' || #{updateUserName} || '%'
        </if>
        <if test="tradeCode != null and tradeCode  != ''">
            AND t.trade_code LIKE '%' || #{tradeCode} || '%'
        </if>
        <if test="productGrade != null and productGrade  != ''">
            AND t.product_grade LIKE '%' || #{productGrade} || '%'
        </if>
        <if test="unit != null and unit  != ''">
            AND t.unit LIKE '%' || #{unit} || '%'
        </if>
        <if test="curr != null and curr  != ''">
            AND t.curr LIKE '%' || #{curr} || '%'
        </if>
        <if test="productType != null and productType  != ''">
            AND t.product_type LIKE '%' || #{productType} || '%'
        </if>
        <if test="headId != null and headId  != ''">
            AND t.head_id LIKE '%' || #{headId} || '%'
        </if>
        <if test="invoiceNo != null and invoiceNo  != ''">
            AND t.invoice_no LIKE '%' || #{invoiceNo} || '%'
        </if>
        <if test="boxNo != null and boxNo  != ''">
            AND t.box_no LIKE '%' || #{boxNo} || '%'
        </if>
        <if test="versionNo != null and versionNo  != ''">
            AND t.version_no LIKE '%' || #{versionNo} || '%'
        </if>
        <if test="dataStatus != null and dataStatus  != ''">
            AND t.data_status LIKE '%' || #{dataStatus} || '%'
        </if>
        <if test="extend1 != null and extend1  != ''">
            AND t.extend1 LIKE '%' || #{extend1} || '%'
        </if>
        <if test="extend2 != null and extend2  != ''">
            AND t.extend2 LIKE '%' || #{extend2} || '%'
        </if>
        <if test="extend3 != null and extend3  != ''">
            AND t.extend3 LIKE '%' || #{extend3} || '%'
        </if>
        <if test="extend4 != null and extend4  != ''">
            AND t.extend4 LIKE '%' || #{extend4} || '%'
        </if>
        <if test="extend5 != null and extend5  != ''">
            AND t.extend5 LIKE '%' || #{extend5} || '%'
        </if>
        <if test="extend6 != null and extend6  != ''">
            AND t.extend6 LIKE '%' || #{extend6} || '%'
        </if>
        <if test="extend7 != null and extend7  != ''">
            AND t.extend7 LIKE '%' || #{extend7} || '%'
        </if>
        <if test="extend8 != null and extend8  != ''">
            AND t.extend8 LIKE '%' || #{extend8} || '%'
        </if>
        <if test="extend9 != null and extend9  != ''">
            AND t.extend9 LIKE '%' || #{extend9} || '%'
        </if>
        <if test="extend10 != null and extend10  != ''">
            AND t.extend10 LIKE '%' || #{extend10} || '%'
        </if>
        <if test="listHeadSid!= null and listHeadSid != ''">
            AND t.list_head_sid LIKE '%' || #{listHeadSid} || '%'
        </if>
    </sql>
    <update id="updatePurchaseListBoxAndQuantity">
        with temp as (
            SELECT LIST_HEAD_SID as sid,
                   LISTAGG(DISTINCT BOX_NO, ',') WITHIN GROUP (ORDER BY PRODUCT_GRADE) AS box_no,
            SUM(QUANTITY)  as quantity
        FROM
            T_BIZ_I_PURCHASE_LIST_BOX
        WHERE head_id = #{headId}
        GROUP BY
            LIST_HEAD_SID
        order by BOX_NO
            )
        update T_BIZ_I_PURCHASE_LIST t1
        set QUANTITY = t.quantity,
            BOX_NO = t.box_no
            from  temp t where t.sid= t1.sid and t1.head_id = #{headId}
    </update>
    <update id="updatePurchaseListBoxBySid">
        update T_BIZ_I_PURCHASE_LIST
        set  BOX_NO = (
            select LISTAGG(distinct BOX_NO,',') from T_BIZ_I_PURCHASE_LIST_BOX where LIST_HEAD_SID= #{listHeadId}
        )
        where sid = #{listHeadId};
    </update>
    <update id="updateJS">
        update T_BIZ_I_PURCHASE_LIST
        set  QUANTITY = (
            select sum(QUANTITY) from T_BIZ_I_PURCHASE_LIST_BOX where LIST_HEAD_SID= #{listHeadId}
        )
        where sid = #{listHeadId};
    </update>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizIPurchaseListBox">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_i_purchase_list_box t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.product_grade,t.box_no
    </select>



    <select id="getSumData" resultType="com.dcjet.cs.dto.dec.BizIPurchaseListBoxSumData">
        select sum(QTY)             as qty,
               sum(QUANTITY)        as quantity
        from T_BIZ_I_PURCHASE_LIST_BOX
        where HEAD_ID = #{headId};
    </select>

    <select id="getPurchaseListBoxByHeadId" resultType="com.dcjet.cs.dec.model.BizIPurchaseListBox">
        select
            <include refid="Base_Column_List"/>
        from T_BIZ_I_PURCHASE_LIST_BOX t
        where t.LIST_HEAD_SID in (
            select sid from T_BIZ_I_PURCHASE_LIST where HEAD_ID in (
                select sid from T_BIZ_I_PURCHASE_HEAD where head_id = #{oldHeadSid}
            )
        );
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_i_purchase_list_box t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>