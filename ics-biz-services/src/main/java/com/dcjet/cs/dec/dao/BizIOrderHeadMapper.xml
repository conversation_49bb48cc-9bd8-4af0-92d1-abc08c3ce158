<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIOrderHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizIOrderHead">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="order_data_status" property="orderDataStatus" jdbcType="VARCHAR"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="party_a" property="partyA" jdbcType="VARCHAR"/>
        <result column="party_b" property="partyB" jdbcType="VARCHAR"/>
        <result column="delivery_date" property="deliveryDate" jdbcType="VARCHAR"/>
        <result column="payment_method" property="paymentMethod" jdbcType="VARCHAR"/>
        <result column="purchase_order_no" property="purchaseOrderNo" jdbcType="VARCHAR"/>
        <result column="import_invoice_no" property="importInvoiceNo" jdbcType="VARCHAR"/>
        <result column="license_no" property="licenseNo" jdbcType="VARCHAR"/>
        <result column="transport_permit_no" property="transportPermitNo" jdbcType="VARCHAR"/>
        <result column="sales_invoice_no" property="salesInvoiceNo" jdbcType="VARCHAR"/>
        <result column="sales_contract_no" property="salesContractNo" jdbcType="VARCHAR"/>
        <result column="purchase_data_status" property="purchaseDataStatus" jdbcType="VARCHAR"/>
        <result column="sales_data_status" property="salesDataStatus" jdbcType="VARCHAR"/>
        <result column="inbound_receipt_status" property="inboundReceiptStatus" jdbcType="VARCHAR"/>
        <result column="outbound_receipt_status" property="outboundReceiptStatus" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="data_status" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="date_of_signing" property="dateOfSigning" jdbcType="TIMESTAMP"/>
        <result column="plan_no" property="planNo" jdbcType="VARCHAR"/>
        <result column="order_confirmation_time" property="orderConfirmationTime" jdbcType="TIMESTAMP"/>
        <result column="appr_status" property="apprStatus" jdbcType="VARCHAR"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
        <result column="serial_no" property="serialNo" jdbcType="NUMERIC"/>
        <result column="head_id" property="headId" jdbcType="NUMERIC"/>
        <result column="is_next" property="isNext" jdbcType="VARCHAR"/>
        <result column="contract_prefix" property="contractPrefix" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.sid, 
            coalesce(t.insert_user,t.insert_user) as insert_user,
            coalesce(t.insert_time,t.insert_time) as insert_time,
            coalesce(t.insert_user_name,t.insert_user_name) as insert_user_name,
            t.update_user,
            t.update_time, 
            t.update_user_name, 
            t.trade_code, 
            t.business_type, 
            t.order_data_status, 
            t.contract_no, 
            t.order_no, 
            t.party_a, 
            t.party_b, 
            t.delivery_date, 
            t.payment_method, 
            t.purchase_order_no, 
            t.import_invoice_no, 
            t.license_no, 
            t.transport_permit_no, 
            t.sales_invoice_no, 
            t.sales_contract_no, 
            t.purchase_data_status, 
            t.sales_data_status, 
            t.inbound_receipt_status, 
            t.outbound_receipt_status,
            case when length(t.version_no) > 2 then substr(t.version_no, 3) else t.version_no end as version_no,
            t.data_status, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10,
            t.date_of_signing,
            t.plan_no,
            t.order_confirmation_time,
            t.appr_status,
            t.note,
            t.serial_no,
            t.head_id,
            t.is_next,
            t.contract_prefix,
            t.is_delete
    </sql>

    <sql id="condition">
        <if test="contractNo != null and contractNo  != ''">
            AND t.contract_no LIKE '%' || #{contractNo} || '%'
        </if>
        <if test="sid != null and sid  != ''">
            AND t.sid LIKE '%' || #{sid} || '%'
        </if>
        <if test="insertUser != null and insertUser  != ''">
            AND t.insert_user LIKE '%' || #{insertUser} || '%'
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ AND coalesce(t.update_time,t.insert_time) >= TO_DATE(#{insertTimeFrom}, 'yyyy-mm-dd')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ AND coalesce(t.update_time,t.insert_time) <  TO_DATE(#{insertTimeTo}, 'yyyy-mm-dd') + 1 ]]>
        </if>
        <if test="insertUserName != null and insertUserName  != ''">
            AND t.insert_user_name LIKE '%' || #{insertUserName} || '%'
        </if>
        <if test="updateUser != null and updateUser  != ''">
            AND t.update_user LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.update_time >= to_timestamp(updateTimeFrom,'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.update_time < to_timestamp(updateTimeTo,'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="updateUserName != null and updateUserName  != ''">
            AND t.update_user_name LIKE '%' || #{updateUserName} || '%'
        </if>
        <if test="tradeCode != null and tradeCode  != ''">
            AND t.trade_code LIKE '%' || #{tradeCode} || '%'
        </if>
        <if test="businessType != null and businessType  != ''">
            AND t.business_type LIKE '%' || #{businessType} || '%'
        </if>
        <if test="orderDataStatus != null and orderDataStatus  != ''">
            AND t.order_data_status LIKE '%' || #{orderDataStatus} || '%'
        </if>
        <if test="contractNo != null and contractNo  != ''">
            AND t.contract_no LIKE '%' || #{contractNo} || '%'
        </if>
        <if test="orderNo != null and orderNo  != ''">
            AND t.order_no LIKE '%' || #{orderNo} || '%'
        </if>
        <if test="partyA != null and partyA  != ''">
            AND t.party_a LIKE '%' || #{partyA} || '%'
        </if>
        <if test="partyB != null and partyB  != ''">
            AND t.party_b LIKE '%' || #{partyB} || '%'
        </if>
        <if test="deliveryDate != null and deliveryDate  != ''">
            AND t.delivery_date LIKE '%' || #{deliveryDate} || '%'
        </if>
        <if test="paymentMethod != null and paymentMethod  != ''">
            AND t.payment_method LIKE '%' || #{paymentMethod} || '%'
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo  != ''">
            AND t.purchase_order_no LIKE '%' || #{purchaseOrderNo} || '%'
        </if>
        <if test="importInvoiceNo != null and importInvoiceNo  != ''">
            AND t.import_invoice_no LIKE '%' || #{importInvoiceNo} || '%'
        </if>
        <if test="licenseNo != null and licenseNo  != ''">
            AND t.license_no LIKE '%' || #{licenseNo} || '%'
        </if>
        <if test="transportPermitNo != null and transportPermitNo  != ''">
            AND t.transport_permit_no LIKE '%' || #{transportPermitNo} || '%'
        </if>
        <if test="salesInvoiceNo != null and salesInvoiceNo  != ''">
            AND t.sales_invoice_no LIKE '%' || #{salesInvoiceNo} || '%'
        </if>
        <if test="salesContractNo != null and salesContractNo  != ''">
            AND t.sales_contract_no LIKE '%' || #{salesContractNo} || '%'
        </if>
        <if test="purchaseDataStatus != null and purchaseDataStatus  != ''">
            AND t.purchase_data_status LIKE '%' || #{purchaseDataStatus} || '%'
        </if>
        <if test="salesDataStatus != null and salesDataStatus  != ''">
            AND t.sales_data_status LIKE '%' || #{salesDataStatus} || '%'
        </if>
        <if test="inboundReceiptStatus != null and inboundReceiptStatus  != ''">
            AND t.inbound_receipt_status LIKE '%' || #{inboundReceiptStatus} || '%'
        </if>
        <if test="outboundReceiptStatus != null and outboundReceiptStatus  != ''">
            AND t.outbound_receipt_status LIKE '%' || #{outboundReceiptStatus} || '%'
        </if>
        <if test="versionNo != null and versionNo  != ''">
            AND t.version_no LIKE '%' || #{versionNo} || '%'
        </if>

        <if test="extend1 != null and extend1  != ''">
            AND t.extend1 LIKE '%' || #{extend1} || '%'
        </if>
        <if test="extend2 != null and extend2  != ''">
            AND t.extend2 LIKE '%' || #{extend2} || '%'
        </if>
        <if test="extend3 != null and extend3  != ''">
            AND t.extend3 LIKE '%' || #{extend3} || '%'
        </if>
        <if test="extend4 != null and extend4  != ''">
            AND t.extend4 LIKE '%' || #{extend4} || '%'
        </if>
        <if test="extend5 != null and extend5  != ''">
            AND t.extend5 LIKE '%' || #{extend5} || '%'
        </if>
        <if test="extend6 != null and extend6  != ''">
            AND t.extend6 LIKE '%' || #{extend6} || '%'
        </if>
        <if test="extend7 != null and extend7  != ''">
            AND t.extend7 LIKE '%' || #{extend7} || '%'
        </if>
        <if test="extend8 != null and extend8  != ''">
            AND t.extend8 LIKE '%' || #{extend8} || '%'
        </if>
        <if test="extend9 != null and extend9  != ''">
            AND t.extend9 LIKE '%' || #{extend9} || '%'
        </if>
        <if test="extend10 != null and extend10  != ''">
            AND t.extend10 LIKE '%' || #{extend10} || '%'
        </if>
        <if test="dateOfSigningFrom != null and dateOfSigningFrom != ''">
            <![CDATA[ and t.date_of_signing >= to_timestamp(dateOfSigningFrom,'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="dateOfSigningTo != null and dateOfSigningTo != ''">
            <![CDATA[ and t.date_of_signing < to_timestamp(dateOfSigningTo,'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="planNo != null and planNo  != ''">
            AND t.plan_no LIKE '%' || #{planNo} || '%'
        </if>
        <if test="orderConfirmationTimeFrom != null and orderConfirmationTimeFrom != ''">
            <![CDATA[ and t.order_confirmation_time >= to_timestamp(orderConfirmationTimeFrom,'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="orderConfirmationTimeTo != null and orderConfirmationTimeTo != ''">
            <![CDATA[ and t.order_confirmation_time < to_timestamp(orderConfirmationTimeTo,'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="apprStatus != null and apprStatus  != ''">
            AND t.appr_status LIKE '%' || #{apprStatus} || '%'
        </if>
        <if test="contractPrefix!= null and contractPrefix != ''">
            AND t.contract_prefix LIKE '%' || #{contractPrefix} || '%'
        </if>
    </sql>

    <!--
         生成进口订单表头
            sid,创建热人,创建时间,创建人姓名,更新人,更新时间,
            更新人姓名,企业代码,业务类型,订单数据状态,合同编号,
            订单编号,客户,供应商,交货日期,付款方式,
            进货单号,进口发票号码,许可证号,运输许可证号,
            销售发票号码,销售合同号,采购数据状态,
            销售数据状态,入库回执状态,出库回执状态,
            版本号,数据状态,扩展1,扩展2,扩展3,扩展4,扩展5,
            扩展6,扩展7,扩展8,扩展9,扩展10,签订日期,计划编号,
            订单确认时间,审批状态,备注,序号,表头ID,是否流入一下个节点

            || lpad(t1.SERIAL_NO + 1,'2','0')
     -->
    <insert id="generateIOrderHead">
        with temp as(
            select coalesce(count(h.HEAD_ID), 0) as SERIAL_NO,
                   t.CONTRACT_NO                 as CONTRACT_PREFIX,
                   t.TRADE_CODE                  as TRADE_CODE
            from (
                     select TRADE_CODE,
                            CASE
                                WHEN INSTR(CONTRACT_NO, 'SH') > 0 THEN SUBSTR(CONTRACT_NO, 1, INSTR(CONTRACT_NO, 'SH') - 1)
                                ELSE CONTRACT_NO
                                END AS CONTRACT_NO
                     from T_BIZ_I_CONTRACT_HEAD
                 ) t
                     left join (select DISTINCT HEAD_ID as HEAD_ID, CONTRACT_NO as CONTRACT_NO
                                from T_BIZ_I_ORDER_LIST
                                where DATA_STATUS != '2') h on t.CONTRACT_NO = h.CONTRACT_NO
            group by t.CONTRACT_NO, t.TRADE_CODE
        )
        insert into T_BIZ_I_ORDER_HEAD (SID, INSERT_USER, INSERT_TIME, INSERT_USER_NAME, UPDATE_USER, UPDATE_TIME,
                                            UPDATE_USER_NAME, TRADE_CODE, BUSINESS_TYPE, ORDER_DATA_STATUS, CONTRACT_NO,
                                            ORDER_NO, PARTY_A, PARTY_B, DELIVERY_DATE, PAYMENT_METHOD,
                                            PURCHASE_ORDER_NO, IMPORT_INVOICE_NO, LICENSE_NO, TRANSPORT_PERMIT_NO,
                                            SALES_INVOICE_NO, SALES_CONTRACT_NO, PURCHASE_DATA_STATUS,
                                            SALES_DATA_STATUS, INBOUND_RECEIPT_STATUS, OUTBOUND_RECEIPT_STATUS,
                                            VERSION_NO, DATA_STATUS, EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5,
                                            EXTEND6, EXTEND7, EXTEND8, EXTEND9, EXTEND10, DATE_OF_SIGNING, PLAN_NO,
                                            ORDER_CONFIRMATION_TIME, APPR_STATUS, NOTE,SERIAL_NO,HEAD_ID,IS_NEXT,CONTRACT_PREFIX)
        select #{headSid},#{userNo}, now(), #{userName},null,null,
               null,#{tradeCode},'1','0',t.CONTRACT_NO,
               CASE
                   WHEN INSTR(t.CONTRACT_NO, 'SH') > 0 THEN SUBSTR(t.CONTRACT_NO, 1, INSTR(t.CONTRACT_NO, 'SH') - 1)
                   ELSE t.CONTRACT_NO
               END  || '-' ,'中国烟草上海进出口有限责任公司',t.SELLER  ,'即发','T/T',
               null,null,null,null,
               null,null,'0',
               '0','0','0',
               '','0',null,null,null,null,null,
               null,null,null,null,null,now(),#{planNo},
               null,'1',null,0,#{sid},'0',t1.CONTRACT_PREFIX
        from T_BIZ_I_CONTRACT_HEAD t

                 left join temp t1 on  (CASE
                                           WHEN INSTR(t.CONTRACT_NO, 'SH') > 0 THEN SUBSTR(t.CONTRACT_NO, 1, INSTR(t.CONTRACT_NO, 'SH') - 1)
                                           ELSE t.CONTRACT_NO
                                           END) = t1.CONTRACT_PREFIX and t.TRADE_CODE = t1.TRADE_CODE
        where t.sid = #{sid};
    </insert>

    <!--
         生成进口订单表体数据
            主键SID,创建人,创建时间,创建人姓名,更新人,更新时间,
            更新人姓名,企业代码,商品牌号,单位,数量,币别,单价,
            总价,表头ID,商品类型,版本号,数据状态,扩展1,扩展2,
            扩展3,扩展4,扩展5,扩展6,扩展7,扩展8,扩展9,扩展10,进口合同表体的SID

            select
                SYS_GUID(),#{userNo}, now(), #{userName},null,null,
                null,#{tradeCode},t.GOODS_BRAND,t.UNIT,t.CONTRACT_QUANTITY,t.CURR,t.UNIT_PRICE,
                t.TOTAL_VALUE,#{headSid},t.GOODS_CATEGORY,'版本号1','0',null,null,null,null,null,null,null,null,null,null,t.sid,h.sid,h.CONTRACT_NO
            from T_BIZ_I_CONTRACT_LIST t
            left join T_BIZ_I_CONTRACT_HEAD h on t.head_id = h.sid
            where t.HEAD_ID = #{sid};
    -->
    <insert id="generateIOrderList">
        INSERT INTO T_BIZ_I_ORDER_LIST (SID, INSERT_USER, INSERT_TIME, INSERT_USER_NAME, UPDATE_USER, UPDATE_TIME,
                                                    UPDATE_USER_NAME, TRADE_CODE, PRODUCT_GRADE, UNIT, QTY, CURR, DEC_PRICE,
                                                    DEC_TOTAL, HEAD_ID, PRODUCT_TYPE, VERSION_NO, DATA_STATUS, EXTEND1, EXTEND2,
                                                    EXTEND3, EXTEND4, EXTEND5, EXTEND6, EXTEND7, EXTEND8, EXTEND9, EXTEND10,CONTRACT_LIST_ID,CONTRACT_HEAD_ID,CONTRACT_NO)
        <![CDATA[
            SELECT
                SYS_GUID(),#{userNo}, now(), #{userName},null,null,
                null,#{tradeCode},l.GOODS_BRAND,l.UNIT,(l.CONTRACT_QUANTITY - COALESCE(b.ORDER_QUANTITY, 0)),l.CURR,l.UNIT_PRICE,
                round(((l.CONTRACT_QUANTITY - COALESCE(b.ORDER_QUANTITY, 0)) *  l.UNIT_PRICE),2) as TOTAL_VALUE,#{headSid},l.GOODS_CATEGORY,'版本1','0',null,null,null,null,null,null,null,null,null,null,l.sid,h.sid,h.CONTRACT_NO
            FROM
                T_BIZ_I_CONTRACT_LIST l
                    LEFT JOIN T_BIZ_I_CONTRACT_HEAD h ON l.HEAD_ID = h.sid
                    LEFT JOIN T_BIZ_MERCHANT m on h.SELLER = m.MERCHANT_CODE and h.TRADE_CODE = m.TRADE_CODE
                    LEFT JOIN (
                        SELECT
                            l.CONTRACT_NO,
                            l.TRADE_CODE,
                            l.PRODUCT_GRADE AS GOODS_BRAND,
                            SUM(l.QTY) AS ORDER_QUANTITY
                        FROM
                            T_BIZ_I_ORDER_LIST l
                                left join T_BIZ_I_ORDER_HEAD h on l.HEAD_ID = h.sid
                        where h.DATA_STATUS  != '2'
                        GROUP BY
                            l.CONTRACT_NO,
                            l.TRADE_CODE,
                            l.PRODUCT_GRADE
                    ) b ON h.CONTRACT_NO = b.CONTRACT_NO AND l.TRADE_CODE = b.TRADE_CODE AND l.GOODS_BRAND = b.GOODS_BRAND
            WHERE
                l.HEAD_ID = #{sid}
              AND COALESCE(b.ORDER_QUANTITY, 0) < l.CONTRACT_QUANTITY
        ]]>;


        with temp as (
            select LISTAGG(DISTINCT CONTRACT_NO,',')
            from T_BIZ_I_ORDER_LIST where HEAD_ID = #{headSid}
        )
        update T_BIZ_I_ORDER_HEAD set CONTRACT_NO  = (select * from temp)
        where sid = #{headSid};
    </insert>


    <!-- 生成进货信息表头
            插入进货信息表头
            主键SID,创建人,创建时间,创建人姓名,更新人,更新时间,
            更新人姓名,企业代码,进货单号,表头ID
            船名航次,预计抵达日期,报关单号,报关日期,
            进货数据状态,进货确认时间,版本号,数据状态,
            扩展1,扩展2,扩展3,扩展4,扩展5,扩展6,
            扩展7,扩展8,扩展9,扩展10,备注,计划编号,序号,业务类型,是否流入下一个节点

            进货单号  和 订单号 保持一致
            || lpad(coalesce(t1.SERIAL_NO,0) + 1,'2','0')
            || lpad(coalesce(t1.SERIAL_NO,0) + 1,'2','0')
    -->
    <insert id="generatePurchaseHead">
        with temp as(
            select coalesce(count(1), 0)       as SERIAL_NO,
                   h.ORDER_NO                 as ORDER_NO,
                   h.TRADE_CODE               as TRADE_CODE
            from T_BIZ_I_PURCHASE_HEAD h
            where h.DATA_STATUS != '2' and  h.ORDER_NO = #{orderNo}
        group by h.ORDER_NO, h.TRADE_CODE
            )
        insert into T_BIZ_I_PURCHASE_HEAD (SID, INSERT_USER, INSERT_TIME, INSERT_USER_NAME, UPDATE_USER,
                                                       UPDATE_TIME, UPDATE_USER_NAME, TRADE_CODE, PURCHASE_ORDER_NO, HEAD_ID,
                                                       VESSEL_VOYAGE, SAILING_DATE, EXPECTED_ARRIVAL_DATE, ENTRY_NO, ENTRY_DATE,
                                                       PURCHASING_DATA_STATUS, PURCHASE_CONFIRMATION_TIME, VERSION_NO,
                                                       DATA_STATUS, EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5, EXTEND6,
                                                       EXTEND7, EXTEND8, EXTEND9, EXTEND10, NOTE, PLAN_NO, SERIAL_NO,ORDER_NO,BUSINESS_TYPE,IS_NEXT)
        select
            #{purchaseHeadSid},#{userNo}, now(), #{userName},null,
            null, null,#{tradeCode},t.ORDER_NO || '01' ,t.sid,
            null,null,null,'',null,
            '0',null,t.VERSION_NO,
            '0',null,null,null,null,null,null,
            null,null,null,null,null,t.PLAN_NO,coalesce(t1.SERIAL_NO,0) + 1,#{orderNo},'1','0'
        from
            T_BIZ_I_ORDER_HEAD t
                left join temp t1 on  t.TRADE_CODE = t1.TRADE_CODE
        where t.sid = #{sid} and t.ORDER_NO = #{orderNo};
    </insert>

    <!--
        生成进货信息表体
            主键SID,创建人,创建时间,创建人姓名,更新人,
            更新时间,更新人姓名,企业代码,商品牌号,单位,数量,
            币别,单价,总价,商品类别,表头ID,折扣率,
            折扣金额(总值*折扣率%),贷款金额(总值-折扣金额),进口发票号,箱号,件数,版本号,
            数据状态,扩展1,扩展2,扩展3,扩展4,扩展5,
            扩展6,扩展7,扩展8,扩展9,扩展10
    -->

    <insert id="generatePurchaseList">
        with temp as (
            select
                l.DISCOUNT_RATE,
                h.PLAN_ID,
                l.PRODUCT_NAME,
                h.TRADE_CODE
            from T_BIZ_I_PLAN_LIST l
                     left join T_BIZ_I_PLAN h on l.HEAD_ID = h.SID
            where h.status  != '2'
        )
        insert into T_BIZ_I_PURCHASE_LIST (SID, INSERT_USER, INSERT_TIME, INSERT_USER_NAME, UPDATE_USER,
                                               UPDATE_TIME, UPDATE_USER_NAME, TRADE_CODE, PRODUCT_GRADE, UNIT, QTY,
                                               CURR, DEC_PRICE, DEC_TOTAL, PRODUCT_TYPE, HEAD_ID, DISCOUNT_RATE,
                                               DISCOUNT_AMOUNT, PAYMENT_AMOUNT, INVOICE_NO, BOX_NO, QUANTITY,
                                               VERSION_NO, DATA_STATUS, EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5,
                                               EXTEND6, EXTEND7, EXTEND8, EXTEND9, EXTEND10,CONTRACT_HEAD_ID,CONTRACT_LIST_ID)
        select
            a.SID,
            a.INSERT_USER,
            a.INSERT_TIME,
            a.INSERT_USER_NAME,
            a.UPDATE_USER,
            a.UPDATE_TIME,
            a.UPDATE_USER_NAME,
            a.TRADE_CODE,
            a.PRODUCT_GRADE,
            a.UNIT,
            a.QTY,
            a.CURR,
            a.DEC_PRICE,
            a.DEC_TOTAL,
            a.PRODUCT_TYPE,
            a.HEAD_ID,
            a.DISCOUNT_RATE,
            a.DISCOUNT_AMOUNT,
            round((a.DEC_TOTAL - a.DISCOUNT_AMOUNT),2) as PAYMENT_AMOUNT,
            a.INVOICE_NO,
            a.BOX_NO,
            a.QUANTITY,
            a.VERSION_NO,
            a.DATA_STATUS,
            a.EXTEND1,
            a.EXTEND2,
            a.EXTEND3,
            a.EXTEND4,
            a.EXTEND5,
            a.EXTEND6,
            a.EXTEND7,
            a.EXTEND8,
            a.EXTEND9,
            a.EXTEND10,
            a.CONTRACT_HEAD_ID,
            a.CONTRACT_LIST_ID
        from (select SYS_GUID()                                               as SID,
                     #{userNo}                                                as INSERT_USER,
                     now()                                                    as INSERT_TIME,
                     #{userName}                                              as INSERT_USER_NAME,
                     null                                                     as UPDATE_USER,
                     null                                                     as UPDATE_TIME,
                     null                                                     as UPDATE_USER_NAME,
                     #{tradeCode}                                             as TRADE_CODE,
                     l.PRODUCT_GRADE                                          as PRODUCT_GRADE,
                     l.UNIT                                                   as UNIT,
                     l.QTY                                                    as QTY,
                     l.CURR                                                   as CURR,
                     l.DEC_PRICE                                              as DEC_PRICE,
                     l.DEC_TOTAL                                              as DEC_TOTAL,
                     l.PRODUCT_TYPE                                           as PRODUCT_TYPE,
                     #{purchaseHeadSid}                                       as HEAD_ID,
                     t.DISCOUNT_RATE                                          as DISCOUNT_RATE,
                     round((l.DEC_TOTAL *  (coalesce(t.DISCOUNT_RATE, 0)/100)), 2)   as DISCOUNT_AMOUNT,
                     null                                                     as PAYMENT_AMOUNT,
                     null                                                     as INVOICE_NO,
                     null                                                     as BOX_NO,
                     null                                                     as QUANTITY,
                     l.VERSION_NO                                             as VERSION_NO,
                     '0'                                                      as DATA_STATUS,
                     null                                                     as EXTEND1,
                     null                                                     as EXTEND2,
                     null                                                     as EXTEND3,
                     null                                                     as EXTEND4,
                     null                                                     as EXTEND5,
                     null                                                     as EXTEND6,
                     null                                                     as EXTEND7,
                     null                                                     as EXTEND8,
                     null                                                     as EXTEND9,
                     null                                                     as EXTEND10,
                     l.CONTRACT_HEAD_ID,
                     l.CONTRACT_LIST_ID
              from T_BIZ_I_ORDER_LIST l
                       left join T_BIZ_I_ORDER_HEAD h on l.HEAD_ID = h.SID
                       left join temp t on l.TRADE_CODE = t.TRADE_CODE and t.PLAN_ID = h.PLAN_NO and t.PRODUCT_NAME = l.PRODUCT_GRADE
              where l.HEAD_ID = #{sid}
             ) a;
    </insert>
    <!-- 复制版本号 -->
    <insert id="copyVersion">
        with temp as (
            select
                max((to_number(substr(coalesce(VERSION_NO,'版本1'),3)))+1) as serial_no
            from T_BIZ_I_ORDER_HEAD t
            where t.ORDER_NO in  (select  distinct  order_no
                                  from T_BIZ_I_ORDER_HEAD t
                                  where SID = #{oldOrderHeadSid}
            ) and t.trade_code = #{tradeCode}
        )
        INSERT INTO T_BIZ_I_ORDER_HEAD(SID, INSERT_USER, INSERT_TIME, INSERT_USER_NAME, UPDATE_USER, UPDATE_TIME,
                                                   UPDATE_USER_NAME, TRADE_CODE, BUSINESS_TYPE, ORDER_DATA_STATUS, CONTRACT_NO,
                                                   ORDER_NO, PARTY_A, PARTY_B, DELIVERY_DATE, PAYMENT_METHOD, PURCHASE_ORDER_NO,
                                                   IMPORT_INVOICE_NO, LICENSE_NO, TRANSPORT_PERMIT_NO, SALES_INVOICE_NO,
                                                   SALES_CONTRACT_NO, PURCHASE_DATA_STATUS, SALES_DATA_STATUS,
                                                   INBOUND_RECEIPT_STATUS, OUTBOUND_RECEIPT_STATUS, VERSION_NO, DATA_STATUS,
                                                   EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5, EXTEND6, EXTEND7, EXTEND8,
                                                   EXTEND9, EXTEND10, DATE_OF_SIGNING, PLAN_NO, ORDER_CONFIRMATION_TIME,
                                                   APPR_STATUS, NOTE, SERIAL_NO, HEAD_ID, IS_NEXT, CONTRACT_PREFIX)
        SELECT #{newOrderHeadSid}, #{insertUserNo}, now(), #{insertUserName}, null, null,
               null, TRADE_CODE, BUSINESS_TYPE, '', CONTRACT_NO,
               ORDER_NO, PARTY_A, PARTY_B, DELIVERY_DATE, PAYMENT_METHOD, '',
               '', LICENSE_NO, TRANSPORT_PERMIT_NO, SALES_INVOICE_NO,
               SALES_CONTRACT_NO, '0', '0',
               '0', '0', (select '版本' || serial_no from temp )  , '0',
               EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5, EXTEND6, EXTEND7, EXTEND8,
               EXTEND9, EXTEND10, DATE_OF_SIGNING, PLAN_NO, null,
               '0', NOTE, SERIAL_NO, HEAD_ID, '0', CONTRACT_PREFIX
        FROM T_BIZ_I_ORDER_HEAD WHERE SID = #{oldOrderHeadSid};





        insert into T_BIZ_I_PURCHASE_HEAD(sid, insert_user, insert_time, insert_user_name, update_user, update_time,
                                                      update_user_name, trade_code, purchase_order_no, head_id, vessel_voyage,
                                                      sailing_date, expected_arrival_date, entry_no, entry_date,
                                                      purchasing_data_status, purchase_confirmation_time, version_no,
                                                      data_status, extend1, extend2, extend3, extend4, extend5, extend6,
                                                      extend7, extend8, extend9, extend10, note, plan_no, serial_no, order_no,
                                                      business_type, is_next)
        select
            #{purchaseHeadSid},#{insertUserNo},now(),#{insertUserName},null,null,
            null,trade_code,purchase_order_no,#{newOrderHeadSid},vessel_voyage,
            sailing_date,expected_arrival_date,entry_no,entry_date,
            '0',null,null,
            '0',extend1,extend2,extend3,extend4,extend5,extend6,
            extend7,extend8,extend9,extend10,note,plan_no,serial_no,order_no,
            business_type,is_next
        from T_BIZ_I_PURCHASE_HEAD
        where HEAD_ID = #{oldOrderHeadSid};




        insert into T_BIZ_I_ORDER_DOCUMENT (SID, BUSINESS_TYPE, DATA_STATUS, VERSION_NO, TRADE_CODE, PARENT_ID,
                                                        INSERT_USER, INSERT_TIME, UPDATE_USER, UPDATE_TIME, INSERT_USER_NAME,
                                                        UPDATE_USER_NAME, EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5, EXTEND6,
                                                        EXTEND7, EXTEND8, EXTEND9, EXTEND10, LICENSE_NUMBER, APPLICATION_DATE,
                                                        EFFECTIVE_DATE, PERMIT_NUMBER, PERMIT_APPLICATION_DATE, NOTE)
        select #{certificateHeadSid} ,BUSINESS_TYPE,DATA_STATUS,VERSION_NO,TRADE_CODE,#{newOrderHeadSid},
               #{insertUserNo}, now(), null, null, #{insertUserName},
               null, EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5, EXTEND6,
               EXTEND7, EXTEND8, EXTEND9, EXTEND10, LICENSE_NUMBER, APPLICATION_DATE,
               EFFECTIVE_DATE, PERMIT_NUMBER, PERMIT_APPLICATION_DATE, NOTE
        from T_BIZ_I_ORDER_DOCUMENT
        where PARENT_ID = #{oldOrderHeadSid};







        insert into T_BIZ_I_SELL_HEAD (SID, HEAD_ID, PURCHASE_ORDER_NUMBER, PURCHASING_UNIT, SELLING_UNIT, TAX_RATE,
                                                   DATE_OF_SALE, REMARK, SALES_DOCUMENT_STATUS, SALES_DATA_CONFIRMATION_TIME,
                                                   SEND_UFIDA, DRAWER, BUSINESS_DATE, INSERT_USER, INSERT_TIME, INSERT_USER_NAME,
                                                   UPDATE_USER, UPDATE_TIME, UPDATE_USER_NAME, TRADE_CODE)
        select #{sellHeadSid},#{newOrderHeadSid},PURCHASE_ORDER_NUMBER,PURCHASING_UNIT,SELLING_UNIT,TAX_RATE,
               DATE_OF_SALE,REMARK,SALES_DOCUMENT_STATUS,SALES_DATA_CONFIRMATION_TIME,
               SEND_UFIDA,DRAWER,BUSINESS_DATE,#{insertUserNo}, now(), #{insertUserName},
               null,null,null,TRADE_CODE
        from T_BIZ_I_SELL_HEAD
        where HEAD_ID = #{oldOrderHeadSid};


        insert into T_BIZ_I_SELL_LIST (SID, HEAD_ID, SALES_CONTRACT_NUMBER, SALES_INVOICE_NUMBER, TRADE_NAME, UNIT,
                                                   QUANTITY, UNIT_PRICE_EXCLUDING_TAX, AMOUNT_OF_TAX, TAX_NOT_INCLUDED,
                                                   TOTAL_VALUE_TAX, INSERT_USER, INSERT_TIME, INSERT_USER_NAME, UPDATE_USER,
                                                   UPDATE_TIME, UPDATE_USER_NAME, TRADE_CODE)
        select sys_guid(),#{sellHeadSid},SALES_CONTRACT_NUMBER,SALES_INVOICE_NUMBER,TRADE_NAME,UNIT,
               QUANTITY,UNIT_PRICE_EXCLUDING_TAX,AMOUNT_OF_TAX,TAX_NOT_INCLUDED,
               TOTAL_VALUE_TAX,#{insertUserNo},now(),#{insertUserName},
               null,null,null,TRADE_CODE
        from T_BIZ_I_SELL_LIST
        where HEAD_ID in (
            select sid from T_BIZ_I_SELL_HEAD where head_id = #{oldOrderHeadSid}
        );




        insert into T_BIZ_I_RECEIPT_HEAD(SID, HEAD_ID, RECEIPT_NUMBER, CONTRACT_NUMBER, ORDER_NUMBER,
                                                     DELIVERY_NUMBER, CONSIGNEE, WAREHOUSE, DELIVERY_DATE, SUPPLIER, SEND_UFIDA,
                                                     INSPECTION_OUTSTOCK, REMARK, CREATE_BY, CREATE_DATE,
                                                     OUTSTOCK_DOCUMENT_STATUS, INSERT_USER, INSERT_TIME, INSERT_USER_NAME,
                                                     UPDATE_USER, UPDATE_TIME, UPDATE_USER_NAME, TRADE_CODE)
        select #{outHeadSid},#{newOrderHeadSid},RECEIPT_NUMBER,CONTRACT_NUMBER,ORDER_NUMBER,
               DELIVERY_NUMBER,CONSIGNEE,WAREHOUSE,DELIVERY_DATE,SUPPLIER,SEND_UFIDA,
               INSPECTION_OUTSTOCK, REMARK, CREATE_BY,CREATE_DATE,
               '0',#{insertUserNo},now(),#{insertUserName},
               null,null,null,TRADE_CODE
        from T_BIZ_I_RECEIPT_HEAD
        where HEAD_ID = #{oldOrderHeadSid};


        insert into T_BIZ_I_RECEIPT_LIST(SID, HEAD_ID, TRADE_NAME, SHIPMENT_QUANTITY, ACTUAL_QUANTITY_ISSUED, UNIT,
                                                     DEC_PRICE, CURR, AMOUNT, INSERT_USER, INSERT_TIME, INSERT_USER_NAME,
                                                     UPDATE_USER, UPDATE_TIME, UPDATE_USER_NAME, TRADE_CODE)
        select sys_guid(),#{outHeadSid},TRADE_NAME,SHIPMENT_QUANTITY,ACTUAL_QUANTITY_ISSUED,UNIT,
               DEC_PRICE,CURR,AMOUNT,#{insertUserNo}, now(),#{insertUserName},
               null,null,null,TRADE_CODE
        from T_BIZ_I_RECEIPT_LIST
        where HEAD_ID in (
            select sid from T_BIZ_I_RECEIPT_HEAD where head_id = #{oldOrderHeadSid}
        );




        INSERT INTO T_BIZ_WAREHOUSE_RECEIPT_HEAD(SID, BUSINESS_TYPE, DATA_STATUS, VERSION_NO, TRADE_CODE, PARENT_ID,
                                                             INSERT_USER, INSERT_TIME, UPDATE_USER, UPDATE_TIME,
                                                             INSERT_USER_NAME, UPDATE_USER_NAME, EXTEND1, EXTEND2, EXTEND3,
                                                             EXTEND4, EXTEND5, EXTEND6, EXTEND7, EXTEND8, EXTEND9, EXTEND10,
                                                             WAREHOUSE_RECEIPT_NUMBER, CONTRACT_NUMBER, ORDER_NO,
                                                             WAREHOUSE_ENTRY_NUMBER, LADING_NUMBER, LADING_DEPARTMENT,
                                                             INVOICE_NUMBER, SUPPLIER, WAREHOUSE, CURR, SELLING_RATE, RATE,
                                                             TAX_INVOICE_DATE, ENTRY_DATE, OUTDATE, DISCOUNT_RATE,
                                                             SEND_TO_YONGYOU, NOTE, STATUS)
        select
            #{inHeadSid},BUSINESS_TYPE,'0',null,TRADE_CODE,#{newOrderHeadSid},
            #{insertUserNo}, now(), null, null,
            #{insertUserName},null,EXTEND1,EXTEND2,EXTEND3,
            EXTEND4,EXTEND5,EXTEND6,EXTEND7,EXTEND8,EXTEND9,EXTEND10,
            WAREHOUSE_RECEIPT_NUMBER, CONTRACT_NUMBER, ORDER_NO,
            WAREHOUSE_ENTRY_NUMBER, LADING_NUMBER, LADING_DEPARTMENT,
            INVOICE_NUMBER, SUPPLIER, WAREHOUSE, CURR, SELLING_RATE, RATE,
            TAX_INVOICE_DATE, ENTRY_DATE, OUTDATE, DISCOUNT_RATE,
            SEND_TO_YONGYOU, NOTE, STATUS
        from
            T_BIZ_WAREHOUSE_RECEIPT_HEAD
        where
            PARENT_ID = #{oldOrderHeadSid};




        insert into T_BIZ_WAREHOUSE_RECEIPT_LIST(SID, BUSINESS_TYPE, DATA_STATUS, VERSION_NO, TRADE_CODE, PARENT_ID,
                                                             INSERT_USER, INSERT_TIME, UPDATE_USER, UPDATE_TIME,
                                                             INSERT_USER_NAME, UPDATE_USER_NAME, EXTEND1, EXTEND2, EXTEND3,
                                                             EXTEND4, EXTEND5, EXTEND6, EXTEND7, EXTEND8, EXTEND9, EXTEND10,
                                                             GOODS_NAME, QTY, UNIT, INVOICE_NUMBER, FOREIGN_UNIT_PRICE,
                                                             RMB_UNIT_PRICE, FOREIGN_PRICES, RMB_PRICES, TARIFF,
                                                             CONSUMPTION_TAX, VALUE_ADDED_TAX, PRODUC_AMOUNT, TAX_AMOUNT,
                                                             COST_AMOUNT, TOTAL_AMOUNT)
        select
            sys_guid(),BUSINESS_TYPE,'0',null,TRADE_CODE,#{newOrderHeadSid},
            #{insertUserNo}, now(), null, null,
            #{insertUserName},null,EXTEND1,EXTEND2,EXTEND3,
            EXTEND4,EXTEND5,EXTEND6,EXTEND7,EXTEND8,EXTEND9,EXTEND10,
            GOODS_NAME, QTY, UNIT, INVOICE_NUMBER, FOREIGN_UNIT_PRICE,
            RMB_UNIT_PRICE, FOREIGN_PRICES, RMB_PRICES, TARIFF,
            CONSUMPTION_TAX, VALUE_ADDED_TAX, PRODUC_AMOUNT, TAX_AMOUNT,
            COST_AMOUNT, TOTAL_AMOUNT
        from T_BIZ_WAREHOUSE_RECEIPT_LIST
        where
            PARENT_ID in (
                select sid from T_BIZ_WAREHOUSE_RECEIPT_HEAD where PARENT_ID = #{oldOrderHeadSid}
            );
    </insert>
    <update id="updateOrderHeadPurchaseNo">
        with temp as (
            select PURCHASE_ORDER_NO from T_BIZ_I_PURCHASE_HEAD where sid = #{purchaseHeadSid}
        )
        update T_BIZ_I_ORDER_HEAD t set PURCHASE_ORDER_NO = (select * from temp) where t.sid = #{sid};
    </update>

    <!-- 作废数据 -->
    <update id="cancelData">
        update T_BIZ_I_ORDER_HEAD set DATA_STATUS = '2',SALES_DATA_STATUS = '2',INBOUND_RECEIPT_STATUS = '2',ORDER_DATA_STATUS = '2',OUTBOUND_RECEIPT_STATUS = '2',PURCHASE_DATA_STATUS = '2' where sid = #{oldOrderHeadSid};
        update T_BIZ_I_ORDER_LIST set DATA_STATUS = '2' where HEAD_ID = #{oldOrderHeadSid};
        update T_BIZ_I_PURCHASE_HEAD set DATA_STATUS = '2' where HEAD_ID = #{oldOrderHeadSid};
        update T_BIZ_I_PURCHASE_LIST set DATA_STATUS = '2' where HEAD_ID in (
            select sid from T_BIZ_I_PURCHASE_HEAD where HEAD_ID = #{oldOrderHeadSid}
        );
        update T_BIZ_I_SELL_HEAD set SALES_DOCUMENT_STATUS = '2' where HEAD_ID = #{oldOrderHeadSid};
        update T_BIZ_I_RECEIPT_HEAD set OUTSTOCK_DOCUMENT_STATUS = '2' where HEAD_ID = #{oldOrderHeadSid};
        update T_BIZ_WAREHOUSE_RECEIPT_HEAD set DATA_STATUS = '2',status = '2' where PARENT_ID = #{oldOrderHeadSid};
        update T_BIZ_WAREHOUSE_RECEIPT_LIST set DATA_STATUS = '2' where PARENT_ID in (
            select sid from T_BIZ_WAREHOUSE_RECEIPT_HEAD where PARENT_ID = #{oldOrderHeadSid}
        );
        update T_BIZ_I_ORDER_DOCUMENT set DATA_STATUS = '2' where PARENT_ID = #{oldOrderHeadSid};
        update T_BIZ_I_PURCHASE_LIST_BOX set DATA_STATUS = '2' where HEAD_ID = #{newOrderHeadSid};
    </update>

    <update id="updateTurnoverSid">
        update T_BIZ_EXPENSE_I_LIST set TURNOVER_SID = #{newSid} where TURNOVER_SID = #{sid};
    </update>
    <update id="updateDeleteFlag">

        update T_BIZ_I_ORDER_HEAD set IS_DELETE = '1', DATA_STATUS = '2',SALES_DATA_STATUS = '2',INBOUND_RECEIPT_STATUS = '2',ORDER_DATA_STATUS = '2',OUTBOUND_RECEIPT_STATUS = '2',PURCHASE_DATA_STATUS = '2' where sid = #{sid};
        update T_BIZ_I_ORDER_LIST set DATA_STATUS = '2' where HEAD_ID = #{sid};
        update T_BIZ_I_PURCHASE_HEAD set DATA_STATUS = '2' where HEAD_ID = #{sid};
        update T_BIZ_I_PURCHASE_LIST set DATA_STATUS = '2' where HEAD_ID in (
            select sid from T_BIZ_I_PURCHASE_HEAD where HEAD_ID = #{sid}
        );
        update T_BIZ_I_SELL_HEAD set SALES_DOCUMENT_STATUS = '2' where HEAD_ID = #{sid};
        update T_BIZ_I_RECEIPT_HEAD set OUTSTOCK_DOCUMENT_STATUS = '2' where HEAD_ID = #{sid};
        update T_BIZ_WAREHOUSE_RECEIPT_HEAD set DATA_STATUS = '2' where PARENT_ID = #{sid};
        update T_BIZ_WAREHOUSE_RECEIPT_LIST set DATA_STATUS = '2' where PARENT_ID in (
            select sid from T_BIZ_WAREHOUSE_RECEIPT_HEAD where PARENT_ID = #{sid}
        );
        update T_BIZ_I_ORDER_DOCUMENT set DATA_STATUS = '2' where PARENT_ID = #{sid};
        update T_BIZ_I_PURCHASE_LIST_BOX set DATA_STATUS = '2' where HEAD_ID = #{sid};
    </update>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizIOrderHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_i_order_head t
        <where>
            <include refid="condition"></include>
            <choose>
                <when test="dataStatus!= null and dataStatus!= ''">
                    AND t.data_status =  #{dataStatus}
                </when>
                <otherwise>
                    AND t.data_status not in ('2')
                </otherwise>
            </choose>
        </where>
        order by t.insert_time desc
    </select>
<!--
    <select id="getIContractList" resultType="com.dcjet.cs.dto.dec.BizIOrderExtractDto">
        SELECT t.sid         AS "sid",
               t.contract_no AS "contractNo",
               t.seller || ' '  || m.MERCHANT_NAME_CN      AS "supplierName"
        FROM T_BIZ_I_CONTRACT_HEAD t
        left join T_BIZ_MERCHANT m on t.SELLER = m.MERCHANT_CODE and t.TRADE_CODE = m.TRADE_CODE
        WHERE t.DATA_STATUS = '1'
        AND t.sid   IN(
                SELECT
                    distinct HEAD_ID
                FROM T_BIZ_I_CONTRACT_LIST t
                WHERE t.head_id NOT IN (
                    SELECT
                        DISTINCT head_id
                    FROM T_BIZ_I_CONTRACT_LIST
                    WHERE executable_quantity = 0 OR coalesce(executable_quantity,'') = ''
                )
          )
          AND t.CONTRACT_NO not in (
            select DISTINCT l.CONTRACT_NO from T_BIZ_I_ORDER_LIST l
                left join T_BIZ_I_ORDER_HEAD h on l.HEAD_ID = h.SID
            where l.TRADE_CODE = #{tradeCode} and h.DATA_STATUS  != '2'
        )
        AND t.TRADE_CODE = #{tradeCode}
        <if test="contractNo!= null and contractNo != ''">
            AND t.CONTRACT_NO LIKE '%' || #{contractNo} || '%'
        </if>
    </select>
    -->




    <!-- 判断是否已经生成进货信息 -->
    <select id="getIsGeneratePurchase" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_PURCHASE_HEAD where HEAD_ID = #{sid};
    </select>


    <!-- 校验多选合同号的供应商是否一致  -->
    <select id="getSuppliers" resultType="java.lang.String">
        select DISTINCT  SELLER from T_BIZ_I_CONTRACT_HEAD
        where sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>



    <select id="getPlanNo" resultType="java.lang.String">
        select  LISTAGG(DISTINCT PLAN_NO,',') from T_BIZ_I_CONTRACT_HEAD
        where sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getOrderHeadTotal" resultType="com.dcjet.cs.dto.dec.BizIOrderHeadTotal">
        select
            sum(QTY) as qtyTotal,
            sum(DEC_TOTAL) as decTotal
        from T_BIZ_I_ORDER_LIST  l
        where exists(
            select 1 from T_BIZ_I_ORDER_HEAD t
            <where>
                t.sid = l.HEAD_ID
                <include refid="condition"></include>
                <choose>
                    <when test="dataStatus!= null and dataStatus!= ''">
                        AND t.data_status =  #{dataStatus}
                    </when>
                    <otherwise>
                        AND t.data_status not in ('2')
                    </otherwise>
                </choose>
            </where>
        )
    </select>
    <select id="checkOrderNoNotCancel" resultType="java.lang.String">
        select
            sid
        from T_BIZ_I_ORDER_HEAD t
        where t.ORDER_NO in  (select  distinct  order_no
                              from T_BIZ_I_ORDER_HEAD t
                              where SID = #{orderHeadSid}

        ) and t.DATA_STATUS != '2' and  sid != #{orderHeadSid};
    </select>


    <select id="getValidOrdersSids" resultType="java.lang.String">
        select sid from T_BIZ_I_ORDER_HEAD
        where ORDER_NO  = #{orderNo} and trade_code = #{tradeCode} and DATA_STATUS != '2'
    </select>

    <select id="checkNextModuleExistEffectiveDataOrder" resultType="java.lang.Integer">
        select count(1) from T_BIZ_EXPENSE_I_LIST l
                                 left join T_BIZ_EXPENSE_I_HEAD h on l.HEAD_ID = h.SID
        where exists(
            select 1 from T_BIZ_I_ORDER_LIST ol
                              left join T_BIZ_I_ORDER_HEAD oh on ol.HEAD_ID = oh.SID
            where ol.HEAD_ID = #{sid}  and ol.PRODUCT_GRADE = l.PRODUCT_NAME and oh.order_no = l.PURCHASE_NUMBER and l.TRADE_CODE = ol.TRADE_CODE
        ) and l.state  != '2'
    </select>
    <select id="checkNextModuleExistEffectiveDataPurchase" resultType="java.lang.Integer">
        select count(1) from T_BIZ_EXPENSE_I_LIST l
                                 left join T_BIZ_EXPENSE_I_HEAD h on l.HEAD_ID = h.SID
        where exists (
            select 1 from T_BIZ_I_PURCHASE_LIST  ol
                              left join T_BIZ_I_PURCHASE_HEAD oh on ol.HEAD_ID = oh.SID
            where oh.HEAD_ID = #{sid}  and ol.PRODUCT_GRADE = l.PRODUCT_NAME and oh.PURCHASE_ORDER_NO = l.PURCHASE_NUMBER  and l.TRADE_CODE = ol.TRADE_CODE
        ) and l.state  != '2'
    </select>
    <select id="checkNextModuleExistPaymentDataOrder" resultType="java.lang.Integer">
        select count(1) from T_BIZ_PAYMENT_NOTIFY_LIST l
                                 left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where exists(
            select 1 from T_BIZ_I_ORDER_LIST ol
                              left join T_BIZ_I_ORDER_HEAD oh on ol.HEAD_ID = oh.SID
            where ol.HEAD_ID = #{headId}  and ol.PRODUCT_GRADE = l.GOODS_NAME and oh.order_no = l.order_no and l.TRADE_CODE = ol.TRADE_CODE
        ) and h.DOC_STATUS  != '2';
    </select>
    <select id="checkNextModuleExistPaymentDataPurchase" resultType="java.lang.Integer">
        select count(1) from T_BIZ_PAYMENT_NOTIFY_LIST l
                                 left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where exists (
            select 1 from T_BIZ_I_PURCHASE_LIST  ol
                              left join T_BIZ_I_PURCHASE_HEAD oh on ol.HEAD_ID = oh.SID
            where oh.HEAD_ID = #{headId}  and ol.PRODUCT_GRADE = l.GOODS_NAME and oh.PURCHASE_ORDER_NO = l.ORDER_NUMBER
        ) and h.DOC_STATUS  != '2';
    </select>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            MERCHANT_CODE as "value",
            MERCHANT_NAME_CN as "label"
        from  T_BIZ_MERCHANT
        where TRADE_CODE = #{tradeCode};
    </select>

    <!-- 根据业务主键获取随附单证的sid -->
    <select id="getAttachmentFile" resultType="com.dcjet.cs.attach.model.Attached">
        SELECT
            SID,
            TRADE_CODE,
            HEAD_ID,
            BUSINESS_TYPE,
            ACMP_TYPE,
            ACMP_NO,
            FILE_NAME,
            NOTE,
            ORIGIN_FILE_NAME,
            FDFS_ID,
            INSERT_USER,
            INSERT_TIME,
            UPDATE_USER,
            UPDATE_TIME,
            INSERT_USER_NAME,
            UPDATE_USER_NAME,
            FILE_SIZE,
            DATA_SOURCE
        FROM
            T_ATTACHED
        WHERE
            HEAD_ID = #{headId};
    </select>
    <select id="getVersionSerialNo" resultType="java.lang.String">
        select
            (to_number(substr(coalesce(max(VERSION_NO),0),3))+1) as serial_no
        from T_BIZ_I_ORDER_HEAD t
        where  t.ORDER_NO = #{orderNo} and t.trade_code = #{tradeCode}
    </select>
    <select id="getCurrentContractSerialNo" resultType="com.dcjet.cs.dto.dec.CurrentContractSerialNo">
        select
            TRADE_CODE as "tradeCode",
            CONTRACT_PREFIX as "contractPrefix",
            max(SERIAL_NO)+1 as "serialNo",
            LPAD(max(SERIAL_NO) + 1,'2','0') as "serialNoStr"
        from
            T_BIZ_I_ORDER_HEAD
        where CONTRACT_PREFIX = #{contractPrefix} and TRADE_CODE = #{tradeCode}
        group by
            CONTRACT_PREFIX,TRADE_CODE
        limit 1
    </select>
    <select id="getIContractList" resultType="com.dcjet.cs.dto.dec.BizIOrderExtractDto">
        <![CDATA[
            select
                distinct
                a.sid,
                a.contractNo,
                a.supplierName
            from
                (
                    SELECT
                        h.sid         as sid,
                        h.CONTRACT_NO as contractNo,
                        l.TRADE_CODE  as tradeCode,
                        l.GOODS_BRAND as goodsBrand,
                        l.CONTRACT_QUANTITY as qty,
                        h.SELLER || ' '  || m.MERCHANT_NAME_CN      AS supplierName
                    FROM
                        T_BIZ_I_CONTRACT_LIST l
                            LEFT JOIN T_BIZ_I_CONTRACT_HEAD h ON l.HEAD_ID = h.sid
                            LEFT JOIN T_BIZ_MERCHANT m on h.SELLER = m.MERCHANT_CODE and h.TRADE_CODE = m.TRADE_CODE
                            LEFT JOIN (
                            SELECT
                                l.CONTRACT_NO,
                                l.TRADE_CODE,
                                l.PRODUCT_GRADE AS GOODS_BRAND,
                                SUM(l.QTY) AS ORDER_QUANTITY
                            FROM
                                T_BIZ_I_ORDER_LIST l
                                    left join T_BIZ_I_ORDER_HEAD h on l.HEAD_ID = h.sid
                            where h.DATA_STATUS  != '2'
                            GROUP BY
                                l.CONTRACT_NO,
                                l.TRADE_CODE,
                                l.PRODUCT_GRADE
                        ) b
                                      ON h.CONTRACT_NO = b.CONTRACT_NO
                                          AND l.TRADE_CODE = b.TRADE_CODE
                                          AND l.GOODS_BRAND = b.GOODS_BRAND
                    WHERE
                        h.DATA_STATUS = '1'
                      AND l.TRADE_CODE = #{tradeCode}
                      AND COALESCE(b.ORDER_QUANTITY, 0) < l.CONTRACT_QUANTITY
                ) a
        ]]>
        <where>
            <if test="contractNo!= null and contractNo != ''">
                AND a.contractNo LIKE '%' || #{contractNo} || '%'
            </if>
        </where>

    </select>

    <select id="getContractNo" resultType="java.lang.String">
        select
            CASE
                WHEN INSTR(CONTRACT_NO, 'SH') > 0 THEN SUBSTR(CONTRACT_NO, 1, INSTR(CONTRACT_NO, 'SH') - 1)
                ELSE CONTRACT_NO
                END AS CONTRACT_NO
        from T_BIZ_I_CONTRACT_HEAD
        where sid = #{contractNoSid}
    </select>
    <select id="getOrderNoList" resultType="java.lang.String">
        SELECT
            DISTINCT
            t.ORDER_NO
        FROM
            T_BIZ_I_ORDER_HEAD t
        WHERE
            t.DATA_STATUS  = '2' and t.CONTRACT_PREFIX = #{contractNo}   and  t.ORDER_NO not in (
                select DISTINCT ORDER_NO from T_BIZ_I_ORDER_HEAD  where   DATA_STATUS != '2'
            ) and t.trade_code = #{tradeCode}
        order by ORDER_NO ASC
    </select>

    <select id="checkOrderNo" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_ORDER_HEAD
        where ORDER_NO = #{orderNo} and trade_code = #{tradeCode} and DATA_STATUS!= '2'
        <if test="sid != null and sid != ''">
            and sid != #{sid}
        </if>
    </select>
    <select id="getPurchaseCount" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_PURCHASE_HEAD where HEAD_ID = #{sid};
    </select>
    <select id="getReceiptHeadCount" resultType="java.lang.Integer">
        select count(1) from T_BIZ_WAREHOUSE_RECEIPT_HEAD where PARENT_ID = #{sid};
    </select>
    <select id="getSellOrOutHeadCount" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_SELL_HEAD where HEAD_ID = #{sid};
    </select>
    <select id="getPurchaseHeadIsNext" resultType="java.lang.String">
        select coalesce(is_next,'0') from T_BIZ_I_PURCHASE_HEAD where HEAD_ID = #{sid};
    </select>
    <select id="getSellOrOutHead" resultType="java.lang.String">
        select coalesce(is_next,'0') from T_BIZ_WAREHOUSE_RECEIPT_HEAD where PARENT_ID = #{sid};
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_i_order_head t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;

        delete from  t_biz_i_order_list t where t.head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;

    </delete>
    <delete id="deleteOrderAllData">
        delete from T_BIZ_I_SELL_LIST  where HEAD_ID in (
            select sid from T_BIZ_I_SELL_HEAD where HEAD_ID = #{orderHeadSid}
        );
        delete from T_BIZ_I_RECEIPT_LIST  where HEAD_ID in (
            select sid from T_BIZ_I_RECEIPT_HEAD where HEAD_ID = #{orderHeadSid}
        );
        delete from T_BIZ_WAREHOUSE_RECEIPT_LIST  where PARENT_ID in (
            select sid from T_BIZ_WAREHOUSE_RECEIPT_HEAD where PARENT_ID = #{orderHeadSid}
        );
        delete from T_BIZ_I_PURCHASE_LIST  where HEAD_ID in (
            select sid from T_BIZ_I_PURCHASE_HEAD where HEAD_ID = #{orderHeadSid}
        );
        delete from T_BIZ_I_PURCHASE_LIST_BOX  where HEAD_ID = #{orderHeadSid};
        delete from T_BIZ_I_ORDER_LIST  where HEAD_ID = #{orderHeadSid};
        delete from T_BIZ_I_ORDER_HEAD  where sid = #{orderHeadSid};
        delete from T_BIZ_I_PURCHASE_HEAD  where HEAD_ID = #{orderHeadSid};
        delete from T_BIZ_I_SELL_HEAD  where HEAD_ID = #{orderHeadSid};
        delete from T_BIZ_I_RECEIPT_HEAD  where HEAD_ID = #{orderHeadSid};
        delete from T_BIZ_WAREHOUSE_RECEIPT_HEAD  where PARENT_ID = #{orderHeadSid};
        delete from T_BIZ_I_ORDER_DOCUMENT  where PARENT_ID = #{orderHeadSid};
        delete from T_ATTACHED where HEAD_ID = #{orderHeadSid};
    </delete>
    <delete id="deletePurchaseAll">
        delete from T_BIZ_I_PURCHASE_LIST  where HEAD_ID in (
            select sid from T_BIZ_I_PURCHASE_HEAD where HEAD_ID = #{sid}
        );
        delete from T_BIZ_I_PURCHASE_LIST_BOX  where HEAD_ID = #{sid};
        delete from T_BIZ_I_PURCHASE_HEAD  where HEAD_ID = #{sid};
    </delete>
</mapper>