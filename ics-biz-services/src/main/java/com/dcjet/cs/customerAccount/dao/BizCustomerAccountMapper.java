package com.dcjet.cs.customerAccount.dao;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.customerAccount.model.BizCustomerAccount;
import com.dcjet.cs.quo.model.BizQuotation;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizCustomerAccount
* <AUTHOR>
* @date: 2025-6-16
*/
public interface BizCustomerAccountMapper extends Mapper<BizCustomerAccount> {
    /**
     * 查询获取数据
     * @param bizCustomerAccount
     * @return
     */
    List<BizCustomerAccount> getList(BizCustomerAccount bizCustomerAccount);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<String> checkIdNotCancel(String sid);
    BizCustomerAccount getMaxVersionNoByContract(BizCustomerAccount bizCustomerAccount);
    List<Attached> getAttachmentFile(@Param("headId") String headId);

    int checkKey(BizCustomerAccount bizCustomerAccount);
}
