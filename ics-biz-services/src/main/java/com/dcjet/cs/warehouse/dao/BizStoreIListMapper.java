package com.dcjet.cs.warehouse.dao;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizStoreIList
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizStoreIListMapper extends Mapper<BizStoreIList> {
    /**
     * 查询获取数据
     * @param bizStoreIList
     * @return
     */
    List<BizStoreIList> getList(BizStoreIList bizStoreIList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    void deleteByHeadSids(List<String> sids);

    BizStoreIList getSumByHeadId(String headId);
}
