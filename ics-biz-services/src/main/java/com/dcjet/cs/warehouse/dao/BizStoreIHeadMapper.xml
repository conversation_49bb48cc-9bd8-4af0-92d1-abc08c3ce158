<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.warehouse.dao.BizStoreIHeadMapper">
    <resultMap id="bizStoreIHeadResultMap" type="com.dcjet.cs.warehouse.model.BizStoreIHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="store_i_no" property="storeINo" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="pur_sale_contract_no" property="purSaleContractNo" jdbcType="VARCHAR" />
		<result column="purchase_order_no" property="purchaseOrderNo" jdbcType="VARCHAR" />
		<result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
		<result column="merchant_code" property="merchantCode" jdbcType="VARCHAR" />
		<result column="selling_rate" property="sellingRate" jdbcType="NUMERIC" />
		<result column="foreign_curr_price" property="foreignCurrPrice" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="price_term" property="priceTerm" jdbcType="VARCHAR" />
		<result column="tariff_price" property="tariffPrice" jdbcType="NUMERIC" />
		<result column="vat_price" property="vatPrice" jdbcType="NUMERIC" />
		<result column="agent_fee" property="agentFee" jdbcType="NUMERIC" />
		<result column="insurance_fee" property="insuranceFee" jdbcType="NUMERIC" />
		<result column="insurance_fee_curr" property="insuranceFeeCurr" jdbcType="NUMERIC" />
		<result column="product_amount_total" property="productAmountTotal" jdbcType="NUMERIC" />
		<result column="fee_amount_total" property="feeAmountTotal" jdbcType="NUMERIC" />
		<result column="tax_amount_total" property="taxAmountTotal" jdbcType="NUMERIC" />
		<result column="cost_amount_total" property="costAmountTotal" jdbcType="NUMERIC" />
		<result column="total_price" property="totalPrice" jdbcType="NUMERIC" />
		<result column="business_date" property="businessDate" jdbcType="TIMESTAMP" />
		<result column="send_finance" property="sendFinance" jdbcType="VARCHAR" />
		<result column="red_flush" property="redFlush" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="appr_status" property="apprStatus" jdbcType="VARCHAR" />
		<result column="STORE_E_STATUS" property="storeEStatus" jdbcType="VARCHAR" />
		<result column="IS_NEXT" property="isNext" jdbcType="VARCHAR" />
		<result column="PURCHASE_NO_MARK" property="purchaseNoMark" jdbcType="VARCHAR" />
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,COALESCE(update_by,create_by) as createrBy
     ,COALESCE(update_user_name,create_user_name) as createrUserName
     ,COALESCE(update_time,create_time) as createrTime
     ,create_by
     ,create_time
     ,create_user_name
     ,update_by
     ,update_time
     ,update_user_name
     ,trade_code
     ,sys_org_code
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,store_i_no
     ,contract_no
     ,pur_sale_contract_no
     ,purchase_order_no
     ,invoice_no
     ,merchant_code
     ,selling_rate
     ,foreign_curr_price
     ,curr
     ,price_term
     ,tariff_price
     ,vat_price
     ,agent_fee
     ,insurance_fee
     ,insurance_fee_curr
     ,product_amount_total
     ,fee_amount_total
     ,tax_amount_total
     ,cost_amount_total
     ,total_price
     ,business_date
     ,send_finance
     ,red_flush
     ,note
     ,status
     ,appr_status
     ,confirm_time
     ,IS_NEXT
     ,STORE_E_STATUS
     ,PURCHASE_NO_MARK
    </sql>
    <sql id="condition">
    <if test="storeINo != null and storeINo != ''">
	  and store_i_no like '%'|| #{storeINo} || '%'
	</if>
    <if test="merchantCode != null and merchantCode != ''">
	  and merchant_code like '%'|| #{merchantCode} || '%'
	</if>
    <if test="status != null and status != ''">
		and status = #{status}
	</if>
        <if test="status == null or status == ''">
            and t.STATUS !='2'
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(update_time,create_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(update_time,create_time) <= DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizStoreIHeadResultMap" parameterType="com.dcjet.cs.warehouse.model.BizStoreIHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_store_i_head t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.update_time,t.create_time) desc
    </select>
    <select id="checkKey" resultType="java.lang.Integer">
        select count(1) from t_biz_store_i_head where store_i_no = #{storeINo} and TRADE_CODE = #{tradeCode} and status != '2'
        <if test="sid != null and sid != ''"> and sid !=#{sid} </if>
    </select>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            MERCHANT_CODE as "value",
            MERCHANT_NAME_CN as "label"
        from  T_BIZ_MERCHANT
        where TRADE_CODE = #{tradeCode};
    </select>
    <select id="getCostIList" resultType="com.dcjet.cs.bi.model.ExpenseIList">
        SELECT el.*,ty.COST_NAME
        FROM T_BIZ_EXPENSE_I_HEAD h
                 LEFT JOIN T_BIZ_EXPENSE_I_LIST  el ON h.sid = el.head_id
                 LEFT JOIN T_BIZ_COST_TYPE ty ON ty.PARAM_CODE = el.EXPENSE_TYPE
        WHERE h.BUSINESS_TYPE = '2'  AND el.PURCHASE_NUMBER IN
                                         (SELECT TRIM(REGEXP_SUBSTR(#{purchaseNumber}, '[^;]+', 1, LEVEL)) AS purchase_no
                                          FROM dual
        CONNECT BY REGEXP_SUBSTR(#{purchaseNumber}, '[^;]+', 1, LEVEL) IS NOT NULL)
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_store_i_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
