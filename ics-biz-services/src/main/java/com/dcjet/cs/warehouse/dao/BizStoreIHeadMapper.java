package com.dcjet.cs.warehouse.dao;
import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizStoreIHead
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizStoreIHeadMapper extends Mapper<BizStoreIHead> {
    /**
     * 查询获取数据
     * @param bizStoreIHead
     * @return
     */
    List<BizStoreIHead> getList(BizStoreIHead bizStoreIHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkKey(BizStoreIHead bizStoreIHead);

    List<Map<String, String>> getOrderSupplierList(String company);
    List<ExpenseIList> getCostIList(String purchaseNumber);
}
