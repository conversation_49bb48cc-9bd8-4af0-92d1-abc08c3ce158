<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.warehouse.dao.BizStoreIListMapper">
    <resultMap id="bizStoreIListResultMap" type="com.dcjet.cs.warehouse.model.BizStoreIList">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="specifications" property="specifications" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="curr_price" property="currPrice" jdbcType="NUMERIC" />
		<result column="rmb_price" property="rmbPrice" jdbcType="NUMERIC" />
		<result column="curr_total_price" property="currTotalPrice" jdbcType="NUMERIC" />
		<result column="rmb_total_price" property="rmbTotalPrice" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,head_id
     ,create_by
     ,create_time
     ,create_user_name
     ,update_by
     ,update_time
     ,update_user_name
     ,trade_code
     ,sys_org_code
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,g_name
     ,specifications
     ,qty
     ,unit
     ,curr_price
     ,rmb_price
     ,curr_total_price
     ,rmb_total_price
    </sql>
    <sql id="condition">
        head_id = #{headId}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizStoreIListResultMap" parameterType="com.dcjet.cs.warehouse.model.BizStoreIList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_store_i_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getSumByHeadId" resultType="com.dcjet.cs.warehouse.model.BizStoreIList">
        select sum(curr_total_price) as curr_total_price,
               sum(rmb_total_price) as rmb_total_price,
               sum(qty) as qty,
               COALESCE(sum(curr_total_price))/COALESCE(sum(qty)) as curr_price,
               COALESCE(sum(rmb_total_price))/COALESCE(sum(qty)) as rmb_price,
               max(g_name) as g_name
        from t_biz_store_i_list t
        where head_id = #{headId}
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_store_i_list t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadSids">
        delete from t_biz_store_i_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
