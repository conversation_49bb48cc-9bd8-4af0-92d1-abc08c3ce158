package com.dcjet.cs.nonAuxiliaryMaterials.dao;


import com.dcjet.cs.dec.model.InComingListSumTotal;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 进过管理-表体列表Mapper
 */
public interface BizNonIncomingGoodsListMapper extends Mapper<BizNonIncomingGoodsList>{

    /**
     * 查询获取数据
     * @param bizIncomingGoodsList
     * @return
     */
    List<BizNonIncomingGoodsList> getList(BizNonIncomingGoodsList bizIncomingGoodsList);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    List<BizNonIncomingGoodsList> getListSumByInvoice(@Param("headId") String headId, @Param("tradeCode") String tradeCode);

    int batchUpdateInvoiceNo(@Param("idList") List<String> idList,
                             @Param("invoiceNo") String invoiceNo,
                             @Param("userNo")String userNo,
                             @Param("userName")String userName);

    List<BizNonIncomingGoodsList> getListByHeadSids(List<String> sids);

    int deleteByHeadId(@Param("headId") String headId);

    InComingListSumTotal getSumTotalByHeadId(@Param("headId") String headId);
}