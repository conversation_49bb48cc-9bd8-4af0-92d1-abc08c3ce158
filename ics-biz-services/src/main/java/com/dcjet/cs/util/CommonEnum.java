package com.dcjet.cs.util;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Auther: Administrator
 * @Date: 2019/5/9 17:07
 * @Description:
 */
public class CommonEnum {
    /**
     * 修改类别
     */
    public enum ModifyMarkType {
        INSERT("I"),
        UPDATE("U");

        private final String modifyMark;

        ModifyMarkType(String modifyMark) {
            this.modifyMark = modifyMark;
        }

        public String getModifyMark() {
            return modifyMark;
        }
    }

    public enum NotificationStatus {
        NOTICE("1 已通知","1"),
        UNNOTICE("0 未通知","0");

        private final String value;

        private final String code;

        NotificationStatus(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (NotificationStatus type : NotificationStatus.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (NotificationStatus type : NotificationStatus.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

        public enum commonMarkEnum {
            STATE_TRADING_IMPORT_CIGARETTES("国营贸易进口卷烟", "1"),
            STATE_TRADING_IMPORT_ACCESSORIES("国营贸易进口辅料", "2"),
            STATE_TRADING_IMPORT_CIGARETTE_EQUIPMENT("国营贸易进口卷烟设备", "3"),
            STATE_TRADING_IMPORT_TOW("国营贸易进口丝束", "4"),
            STATE_TRADING_DOMESTIC_PURCHASE_AND_SALE_TOW("国营贸易内购内销丝束", "5"),
            NON_STATE_TRADING_IMPORT_ACCESSORIES("非国营贸易进口辅料", "6"),
            PROCESSING_TRADE_IMPORT_THIN_SHEETS("出料加工进口薄片", "7"),
            EXPORT_CIGARETTE_MACHINERY_EQUIPMENT("出口烟机设备", "8"),
            EXPORT_ACCESSORIES("出口辅料", "9");

            private final String value;

            private final String code;

            commonMarkEnum(String value, String code) {
                this.value = value;
                this.code = code;
            }

            public String getCode() {
                return code;
            }

            public String getValue() {
                return value;
            }

            /**
             * 根据类型的 code，返回类型的 value
             *
             * @param code 类型 code
             */
            public static String getValue(String code) {
                for (commonMarkEnum type : commonMarkEnum.values()) {
                    if (type.getCode().equals(code)) {
                        return type.getValue();
                    }
                }
                return "";
            }
        }

    public enum businessTypeEnum {
        type_1("1 国营贸易进口卷烟","1"),
        type_2("2 国营贸易进口辅料","2"),
        type_3("3 国营贸易进口卷烟设备","3"),
        type_4("4 国营贸易进口丝束","4"),
        type_5("5 国营贸易内购内销丝束","5"),
        type_6("6 非国营贸易进口辅料","6"),
        type_7("7 出料加工进口薄片","7"),
        type_8("8 出口烟机设备","8"),
        type_9("9 出口辅料","9");

        private final String value;

        private final String code;

        businessTypeEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (businessTypeEnum type : businessTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum isNotEnum {
        type_1("是","0"),
        type_2("否","1");

        private final String value;

        private final String code;

        isNotEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (isNotEnum type : isNotEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum YearConstEnum {
        FIRST_HALF_YEAR("0 上半年", "0"),
        SECOND_HALF_YEAR("1 下半年", "1");

        private final String label;
        private final String value;

        YearConstEnum(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 value，返回类型的 label
         *
         * @param value 类型 value
         */
        public static String getValue(String value) {
            for (YearConstEnum year : YearConstEnum.values()) {
                if (year.getValue().equals(value)) {
                    return year.getLabel();
                }
            }
            return "";
        }
    }

    public enum OrderStatusEnum {
        DRAFT("0 编制", "0"),
        CONFIRMED("1 确认", "1"),
        CANCELLED("2 作废", "2");

        private final String label;
        private final String value;

        OrderStatusEnum(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 value，返回类型的 label
         *
         * @param value 类型 value
         */
        public static String getValue(String value) {
            for (OrderStatusEnum status : OrderStatusEnum.values()) {
                if (status.getValue().equals(value)) {
                    return status.getLabel();
                }
            }
            return "";
        }
    }

    /**
     * 订单审批状态
     */
    public enum OrderApprStatusEnum {
        NOT_INVOLVED("0 不涉及审批", "0"),
        NOT_APPROVED("1 未审批", "1"),
        APPROVING("2 审批中", "2"),
        APPROVED("3 审批通过", "3"),
        APPROVAL_REJECTED("4 审批退回", "4");

        private final String label;
        private final String value;

        OrderApprStatusEnum(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 value，返回类型的 label
         *
         * @param value 类型 value
         */
        public static String getValue(String value) {
            for (OrderApprStatusEnum status : OrderApprStatusEnum.values()) {
                if (status.getValue().equals(value)) {
                    return status.getLabel();
                }
            }
            return "";
        }
    }

    public enum OrderAuditStatusEnum {
        NOT_INVOLVED("0 未发送", "0"),
        NOT_APPROVED("1 已发送审核", "1"),
        APPROVING("2 审核通过", "2");

        private final String label;
        private final String value;

        OrderAuditStatusEnum(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 value，返回类型的 label
         *
         * @param value 类型 value
         */
        public static String getValue(String value) {
            for (OrderAuditStatusEnum status : OrderAuditStatusEnum.values()) {
                if (status.getValue().equals(value)) {
                    return status.getLabel();
                }
            }
            return "";
        }
    }

    public enum BusinessDistinctionEnum {
        SHANG_HAI("0 上海业务", "0"),
        OTHER_PLACE("1 外地业务", "1");

        private final String label;
        private final String value;

        BusinessDistinctionEnum(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 value，返回类型的 label
         *
         * @param value 类型 value
         */
        public static String getValue(String value) {
            for (BusinessDistinctionEnum status : BusinessDistinctionEnum.values()) {
                if (status.getValue().equals(value)) {
                    return status.getLabel();
                }
            }
            return "";
        }
    }

    public enum operationsEnum {
        INSERT("新增", "INSERT"),
        UPDATE("修改", "UPDATE"),
        DELETE("删除", "DELETE"),
        SELECT("查询", "SELECT");

        private final String value;

        private final String code;

        operationsEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (operationsEnum type : operationsEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 数据状态 1: 修改, 2: 删除, 3: 新增
     */
    public enum modifyMarkEnum {
        MODIFY_MARK_0("0 不变", "0"),
        MODIFY_MARK_1("1 修改", "1"),
        MODIFY_MARK_2("2 删除", "2"),
        MODIFY_MARK_3("3 新增", "3");

        private final String value;

        private final String code;

        modifyMarkEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (modifyMarkEnum type : modifyMarkEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 保完税标识枚举
     */
    public enum BondMarkEnum {
        BOND("0 保税", "0"),
        NOBOND("1 非保税", "1");

        private final String value;

        private final String code;

        BondMarkEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (BondMarkEnum type : BondMarkEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (BondMarkEnum type : BondMarkEnum.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 物料类型枚举 E: 成品, I: 料件, 2: 设备, 3: 其他, 4: 半成品, N: 无
     */
    public enum GMarkEnum {
        IMG("I 料件", "I"),
        EXG("E 成品", "E"),
        EQUIP("2 设备", "2"),
        OTHER("3 其他", "3"),
        HALF("4 半成品", "4"),
        PACK("5 包材", "5"),
        NONE("N 无", "N");

        private final String value;

        private final String code;

        GMarkEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (GMarkEnum type : GMarkEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (GMarkEnum type : GMarkEnum.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 保完税标识枚举
     */
    public enum GMarkBondEnum {
        IMG("料件", "I"),
        EXG("成品", "E");

        private final String value;

        private final String code;

        GMarkBondEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (GMarkBondEnum type : GMarkBondEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 暂存 "0"; 待审核 "2"; 修改 "3"; 内审通过 "8"; 草单审查 "9"; 清单撤回 "C"; 内审退回 "-1"; 发送备案 "JA"; 备案通过 "JC"; 备案退回 "JD";
     * 发送备案中(提交后端任务, 单损耗) "J0"; 发送备案中(提交后端任务, 单损耗) "J1";
     */
    public enum APPR_STAUTS_ENUM {
        APPR_STAUTS_0("0 暂存", "0"),
        APPR_STAUTS_2("2 待审核", "2"),
        APPR_STAUTS_3("3 修改", "3"),
        APPR_STAUTS_8("8 内审通过", "8"),
        APPR_STAUTS_9("9 草单生成", "9"),
        APPR_STAUTS_C("C 清单撤回", "C"),
        APPR_STAUTS_B("-1 内审退回", "-1"),
        APPR_STAUTS_JA("JA 发送备案", "JA"),
        APPR_STAUTS_JC("JC 备案通过", "JC"),
        APPR_STAUTS_JD("JD 备案退回", "JD"),
        APPR_STAUTS_J0("J0 发送备案中", "J0"),
        APPR_STAUTS_J1("J1 发送备案中", "J1");

        private final String value;
        private final String code;

        APPR_STAUTS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (APPR_STAUTS_ENUM type : APPR_STAUTS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }
   /* KJ3状态
    0暂存，1已签收*/
    public enum KJ_STAUTS_ENUM {
        APPR_STAUTS_0("0 暂存", "0"),
        APPR_STAUTS_1("1 已签收", "1");

        private final String value;
        private final String code;

        KJ_STAUTS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (KJ_STAUTS_ENUM type : KJ_STAUTS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * -1 退回; 0 暂存; 1 待分单; 2 已分单; 3 预报完结
     */
    public enum PRE_STAUTS_ENUM {
        PRE_STAUTS_B("-1 退回", "-1"),
        PRE_STAUTS_0("0 暂存", "0"),
        PRE_STAUTS_1("1 待分单", "1"),
        PRE_STAUTS_2("2 已分单", "2"),
        PRE_STAUTS_3("3 预报完结", "3");

        private final String value;
        private final String code;

        PRE_STAUTS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        public static String getValue(String code) {
            for (PRE_STAUTS_ENUM type : PRE_STAUTS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 单据标记 00: 一般贸易 11: 金二手册 12: H200手册 21: 金二账册 22: H2000账册; 31: 点讯通
     */
    public enum EMS_TYPE_ENUM {
        EMS_TYPE_00("一般贸易", "00"),
        EMS_TYPE_11("金二手册", "11"),
        EMS_TYPE_12("H200手册", "12"),
        EMS_TYPE_21("金二账册", "21"),
        EMS_TYPE_22("H2000账册", "22"),
        EMS_TYPE_31("点讯通", "31");

        private final String value;
        private final String code;

        EMS_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (EMS_TYPE_ENUM type : EMS_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 待归类数据来源
     */
    public enum matSourceEnum {
        MAT_SOURCE_0("手工录入", "0"),
        MAT_SOURCE_1("ERP接口", "1"),
        MAT_SOURCE_2("导入", "2");

        private final String value;

        private final String code;

        matSourceEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (matSourceEnum type : matSourceEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum matOrgSourceEnum {
        MAT_ORG_SOURCE_0("手工录入", "0"),
        MAT_ORG_SOURCE_1("待归类提取", "1"),
        MAT_ORG_SOURCE_2("导入", "2"),
        MAT_ORG_SOURCE_3("同步", "3");

        private final String value;

        private final String code;

        matOrgSourceEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (matOrgSourceEnum type : matOrgSourceEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 待归类数据处理状态
     */
    public enum handleMarkEnum {
        HANDLE_MARK_0("新增", "0"),
        HANDLE_MARK_1("已处理", "1"),
        HANDLE_MARK_2("已匹配", "2");

        private final String value;

        private final String code;

        handleMarkEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (handleMarkEnum type : handleMarkEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 进出口标志
     */
    public enum I_E_MARK_ENUM {
        I_MARK("I 进口", "I"),
        E_MARK("E 出口", "E");

        private final String value;

        private final String code;

        I_E_MARK_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (I_E_MARK_ENUM type : I_E_MARK_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 清单状态
     */
    public enum BillEnum {
        BILLSTATUS_B("未发送", "-1"),
        BILLSTATUS_0("发送失败", "0"),
        BILLSTATUS_1("发送成功", "1"),
        BILLSTATUS_2("发送中", "2"),
        BILLSTATUS_3("清单撤回", "3"),
        BILLSTATUS_A0("暂存", "A0"),
        BILLSTATUS_B1("申报中(报文未生成)", "B1"),
        BILLSTATUS_C1("申报中(报文已生成)", "C1"),
        BILLSTATUS_D1("申报失败(报文生成失败)", "D1"),
        BILLSTATUS_J0("报文已接收", "J0"),
        BILLSTATUS_J1("海关终审通过", "J1"),
        BILLSTATUS_J2("转人工", "J2"),
        BILLSTATUS_J3("退单", "J3"),
        BILLSTATUS_DL("删单", "DL"),
        BILLSTATUS_4("预审批通过", "4"),
        BILLSTATUS_5("通过(未核扣)", "5"),
        BILLSTATUS_6("通过(预核扣)", "6"),
        BILLSTATUS_Y("入库成功", "Y"),
        BILLSTATUS_Z("入库失败", "Z"),
        BILLSTATUS_JDL("删单", "JDL");

        private final String value;

        private final String code;

        BillEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (BillEnum type : BillEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 运保杂类型
     */
    public enum FeeMarkEnum {
        MARK_1("1 率", "1"),
        MARK_2("2 单价", "2"),
        MARK_3("3 总价", "3");

        private final String value;

        private final String code;

        FeeMarkEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (FeeMarkEnum type : FeeMarkEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 运保杂类型
     */
    public enum FeeMarkEnumNoCode {
        MARK_1("率", "1"),
        MARK_2("单价", "2"),
        MARK_3("总价", "3");

        private final String value;

        private final String code;

        FeeMarkEnumNoCode(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (FeeMarkEnumNoCode type : FeeMarkEnumNoCode.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 集装箱类型
     */
    public enum ContainerEnum {
        CONTAINER_11("普通2*标准箱(L)", "11"),
        CONTAINER_12("冷藏2*标准箱(L)", "12"),
        CONTAINER_13("罐式2*标准箱(L)", "13"),
        CONTAINER_21("普通标准箱(S)", "21"),
        CONTAINER_22("冷藏标准箱(S)", "22"),
        CONTAINER_23("罐式标准箱(S)", "23"),
        CONTAINER_31("其他标准箱(S)", "31"),
        CONTAINER_32("其他2*标准箱(L)", "32");

        private final String value;

        private final String code;

        ContainerEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (ContainerEnum type : ContainerEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 数据来源 1 录入; 2 导入; 3 数据提取; 4 集报提取; 5 深加工生成; 6 内销生成; 7 减免税设备; 8 预报单生成(克诺尔);
     * 9 进料成品退运; 10 非保税成品退运; 11 修理物品; 12 暂时进出口; 13 非保成品退运(退运货物); 14 大金订单提取
     */
    public enum dataSourceEnum {
        SOURCE_1("手工录入", "1"),
        SOURCE_2("导入", "2"),
        SOURCE_3("数据提取", "3"),
        SOURCE_4("集报提取", "4"),
        SOURCE_5("深加工生成", "5"),
        SOURCE_6("内销生成", "6"),
        SOURCE_7("减免税设备", "7"),
        SOURCE_8("预报单生成(克诺尔)", "8"),
        SOURCE_9("进料成品退运", "9"),
        SOURCE_10("非保税成品退运", "10"),
        SOURCE_11("修理物品", "11"),
        SOURCE_12("暂时进出境", "12"),
        SOURCE_13("非保成品退运(退运货物)", "13"),
        SOURCE_14("大金订单提取", "14"),
        SOURCE_15("OCR生成", "15"),
        SOURCE_16("料件复出", "16"),
        SOURCE_17("出口计划生成", "17");

        private final String value;

        private final String code;

        dataSourceEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (dataSourceEnum type : dataSourceEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 清单类型
     */
    public enum billListType {
        t0("普通清单", "0"),
        t1("集报清单", "1"),
        t3("先入区后报关", "3"),
        t4("简单加工", "4"),
        t5("保税展示交易", "5"),
        t6("区内流转", "6"),
        t7("区港联动", "7"),
        t8("保税电商", "8"),
        t9("一纳成品内销", "9");

        billListType(String value, String code) {
            this.code = code;
            this.value = value;
        }

        private String code;
        private String value;

        public String getValue() {
            return value;
        }

        public void setName(String name) {
            this.value = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public static String getValue(String code) {
            for (billListType type : billListType.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }


    }

    /**
     * 报关单归并类型MergeType
     */
    public enum MERGE_TYPE {
        MERGE_TYPE_0("0 自动归并", "0"),
        MERGE_TYPE_1("1 人工归并", "1"),
        MERGE_TYPE_2("2 不归并", "2"),
        MERGE_TYPE_3("3 同型号", "3"),
        MERGE_TYPE_4("4 按备案料号归并", "4"),
        MERGE_TYPE_5("5 按模拟项号归并", "5");

        private final String value;

        private final String code;

        MERGE_TYPE(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (MERGE_TYPE type : MERGE_TYPE.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 报关单归并类型
     */
    public enum BILL_TYPE {
        BILL_TYPE_1("不归并", "1"),
        BILL_TYPE_2("按企业料号归并", "2"),
        BILL_TYPE_3("按备案料号归并", "3"),
        BILL_TYPE_4("人工归并", "4");

        private final String value;

        private final String code;

        BILL_TYPE(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (BILL_TYPE type : BILL_TYPE.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 清单上对应报关单类型
     */
    public enum ENTRY_TYPE {
        ENTRY_TYPE_1("进口报关单", "1"),
        ENTRY_TYPE_2("出口报关单", "2"),
        ENTRY_TYPE_3("进境备案清单", "3"),
        ENTRY_TYPE_4("出境备案清单", "4"),
        ENTRY_TYPE_5("进境两单一审备案清单", "5"),
        ENTRY_TYPE_6("出境两单一审备案清单", "6"),
        ENTRY_TYPE_B("转关提前进境备案清单", "B"),
        ENTRY_TYPE_C("转关提前出境备案清单", "C"),
        ENTRY_TYPE_F("出口二次转关", "F"),
        ENTRY_TYPE_G("进口提前/工厂验放报关单", "G"),
        ENTRY_TYPE_H("出口提前/工厂验放报关单", "H"),
        ENTRY_TYPE_I("进口提前/暂时进口报关单", "I"),
        ENTRY_TYPE_J("出口提前/暂时出口报关单", "J"),
        ENTRY_TYPE_K("进口提前/中欧班列报关单", "K"),
        ENTRY_TYPE_L("出口提前/中欧班列报关单", "L"),
        ENTRY_TYPE_M("出口提前/市场采购报关单", "M"),
        ENTRY_TYPE_N("出口提前/空运联程报关单", "N"),
        ENTRY_TYPE_O("进口提前/工厂验放备案清单", "O"),
        ENTRY_TYPE_P("出口提前/工厂验放备案清单", "P"),
        ENTRY_TYPE_Q("进口提前/暂时进口备案清单", "Q"),
        ENTRY_TYPE_X("进口两步申报报关单", "X"),
        ENTRY_TYPE_Y("进口两步申报备案清单", "Y"),
        ENTRY_TYPE_E("进口两步申报一次录入报关单", "e");

        private final String value;

        private final String code;

        ENTRY_TYPE(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (ENTRY_TYPE type : ENTRY_TYPE.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 报关标志 1:报关,2:非报关
     */
    public enum DCLCUS_MARK {
        MARK_1("1 报关", "1"),
        MARK_2("2 非报关", "2");

        private final String value;

        private final String code;

        DCLCUS_MARK(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (DCLCUS_MARK type : DCLCUS_MARK.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 报关类型 1: 关联报关, 2: 对应报关
     */
    public enum DCLCUS_TYPE {
        MARK_1("1 关联报关", "1"),
        MARK_2("2 对应报关", "2");

        private final String value;

        private final String code;

        DCLCUS_TYPE(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (DCLCUS_TYPE type : DCLCUS_TYPE.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 报关单类型 0:有纸报关,D:无纸带清单报关,L:有纸带清单报关,M:通关无纸化,W:无纸报关
     */
    public enum ENTRY_MARK {
        ENTRY_MARK_0("0 有纸报关", "0"),
        ENTRY_MARK_D("D 无纸带清单报关", "D"),
        ENTRY_MARK_L("L 有纸带清单报关", "L"),
        ENTRY_MARK_M("M 通关无纸化", "M"),
        ENTRY_MARK_W("W 无纸报关", "W");

        private final String value;

        private final String code;

        ENTRY_MARK(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (ENTRY_MARK type : ENTRY_MARK.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 预录入单表体状态
     */
    public enum LockEnum {
        IMG("可修改", "0"),
        EXG("已锁定", "1");

        private final String value;

        private final String code;

        LockEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (LockEnum type : LockEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 计费种类:1票数 2计费重量 3车柜型 4天/重量计 5天/体积计 6按体积计费 7快件
     */
    public enum CHARGE_TYPE_ENUM {

        CHARGE_TYPE_1("票数", "1"),
        CHARGE_TYPE_2("计费重量", "2"),
        CHARGE_TYPE_3("车柜型", "3"),
        CHARGE_TYPE_4("天/重量计", "4"),
        CHARGE_TYPE_5("天/体积计", "5"),
        CHARGE_TYPE_6("按体积计费", "6"),
        CHARGE_TYPE_7("分段式收费", "7"),
        CHARGE_TYPE_999("快件", "999");

        private final String value;
        private final String code;

        CHARGE_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        public static String getValue(String code) {
            for (CHARGE_TYPE_ENUM type : CHARGE_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 计算类型（1 一般报价单票数 2 一般报价单计费重量 3 一般报价单车柜型 4 天/重量计 5 天/体积计 6 体积 7 快件报价单）
     */
    public enum CAL_TYPE_ENUM {

        CAL_TYPE_1("一般报价单票数", "1"),
        CAL_TYPE_2("一般报价单计费重量", "2"),
        CAL_TYPE_3("一般报价单车柜型", "3"),
        CAL_TYPE_4("天/重量计", "4"),
        CAL_TYPE_5("天/体积计", "5"),
        CAL_TYPE_6("体积", "6"),
        CAL_TYPE_999(" 快件报价单", "999");

        private final String value;
        private final String code;

        CAL_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        public static String getValue(String code) {
            for (CAL_TYPE_ENUM type : CAL_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 征免方式 对应监管方式默认值
     */
    public enum DUTY_MODE_ENUM {
        DUTY_MODE_ENUM_I0110("I", "0110", "1"),
        DUTY_MODE_ENUM_I0245("I", "0245", "1"),
        DUTY_MODE_ENUM_I0644("I", "0644", "1"),
        DUTY_MODE_ENUM_I0214("I", "0214", "3"),
        DUTY_MODE_ENUM_I0615("I", "0615", "3"),
        DUTY_MODE_ENUM_I0255("I", "0255", "3"),
        DUTY_MODE_ENUM_I0654("I", "0654", "3"),
        DUTY_MODE_ENUM_I0258("I", "0258", "3"),
        DUTY_MODE_ENUM_I0657("I", "0657", "3"),
        DUTY_MODE_ENUM_I4400("I", "4400", "3"),
        DUTY_MODE_ENUM_I4600("I", "4600", "3"),

        DUTY_MODE_ENUM_E0110("E", "0110", "1"),
        DUTY_MODE_ENUM_E0245("E", "0245", "1"),
        DUTY_MODE_ENUM_E0644("E", "0644", "1"),
        DUTY_MODE_ENUM_E0214("E", "0214", "3"),
        DUTY_MODE_ENUM_E0615("E", "0615", "3"),
        DUTY_MODE_ENUM_E0255("E", "0255", "3"),
        DUTY_MODE_ENUM_E0654("E", "0654", "3"),
        DUTY_MODE_ENUM_E0258("E", "0258", "3"),
        DUTY_MODE_ENUM_E0657("E", "0657", "3"),
        DUTY_MODE_ENUM_E0265("E", "0265", "3"),
        DUTY_MODE_ENUM_E0300("E", "0300", "3"),
        DUTY_MODE_ENUM_E0664("E", "0664", "3"),
        DUTY_MODE_ENUM_E0700("E", "0700", "3");

        private final String value;
        private final String IEMark;
        private final String code;

        DUTY_MODE_ENUM(String IEMark, String code, String value) {
            this.IEMark = IEMark;
            this.value = value;
            this.code = code;
        }

        public String getIEMark() {
            return IEMark;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String IEMark, String code) {
            for (DUTY_MODE_ENUM type : DUTY_MODE_ENUM.values()) {
                if (type.getIEMark().equals(IEMark) && type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 发送目的地
     */
    public enum SEND_DEST {
        SEND_DEST_0("立交桥", "0"),
        SEND_DEST_1("报关小助手", "1"),
        SEND_DEST_2("立交桥", "2"),
        SEND_DEST_3("报关小助手", "3"),
        SEND_DEST_9("报关行", "9");

        private final String value;

        private final String code;

        SEND_DEST(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (SEND_DEST type : SEND_DEST.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 报关单状态
     */
    public enum ENTRY_STATUS {
        ENTRY_STATUS_1("已到港", "1"),
        ENTRY_STATUS_2("已申报", "2"),
        ENTRY_STATUS_3("已报关", "3"),
        ENTRY_STATUS_4("查验中", "4"),
        ENTRY_STATUS_5("已完税", "5"),
        ENTRY_STATUS_6("已放行", "6"),
        ENTRY_STATUS_7("已通知", "7"),
        ENTRY_STATUS_8("已到货", "8"),
        ENTRY_STATUS_9("已结算", "9");

        private final String value;

        private final String code;

        ENTRY_STATUS(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (ENTRY_STATUS type : ENTRY_STATUS.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum ImportType {
        /**
         * 新增
         */
        Add("1"),
        /**
         * 修改 存在的先删除，再插入；不存在的直接插入
         */
        Modify("2"),
        /**
         * 删除
         */
        Delete("3"),
        /**
         * 覆盖，先删后插
         */
        Cover("4");

        private final String value;

        ImportType(String val) {
            this.value = val;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 接口类型
     */
    public enum ApiType {
        /**
         * 手册料件备案api
         */
        EML_IMG_API("手册料件备案api"),
        /**
         * 手册成品备案api
         */
        EML_EXG_API("手册成品备案api"),
        /**
         * 账册料件备案api
         */
        EMS_IMG_API("账册料件备案api"),
        /**
         * 账册成品备案api
         */
        EMS_EXG_API("账册成品备案api"),
        /**
         * 核注清单api
         */
        INVT_API("核注清单api"),
        /**
         * 报关立交桥api
         */
        BGLJQ_API("报关立交桥api"),
        /**
         * 报关金二api
         */
        BGHG_API("报关金二api"),
        /**
         * 点迅通核注清单上传api
         */
        DXT_BILL_API("点迅通核注清单上传api"),
        /**
         * 概要申报
         */
        SEND_OUTLINE("发送概要申报api"),
        /**
         * 点迅通核注清单删除api
         */
        DXT_BILL_CANCEL("点迅通核注清单删除api");
        private final String value;

        ApiType(String val) {
            this.value = val;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 参数类型
     */
    public enum ParamType {
        /**
         * 报关立交桥返回结果
         */
        BgljqReplyType("BGLJQ_REPLY_TYPE");

        private final String value;

        ParamType(String val) {
            this.value = val;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 预警类型
     */
    public enum WarringEnum {
        CARD("CS-EARLYWARNING-IDCARD", "CARD"),         // 证件卡类预警管理
        BAIL("CS-EARLYWARNING-BOND", "BAIL"),           // 保金保函预警管理
        SPILL("CS-EARLYWARNING-SPILL", "SPILL"),        // 生产能力证明超金额
        EXPIRE("CS-EARLYWARNING-EXPIRE", "EXPIRE"),     // 外发加工到期预警
        EQUIP("CS-EARLYWARNING-EQUIP", "EQUIP"),        // 减免税设备解除监管
        EXPORT("CS-EARLYWARNING-IE", "EXPORT"),         // 暂时进出口预警
        REPAIR("CS-EARLYWARNING-REPAIR", "REPAIR"),     // 修理物品预警
        PRICE("CS-EARLYWARNING-PRICE", "PRICE"),        // 价格预警管理
        WT("CS-EARLYWARNING-WT", "WT"),                 // 净重预警管理
        MARGIN("CS-EARLYWARNING-MANUAL", "MARGIN"),     // 手册余量预警
        THIF("THIF", "THIF");                           // 到货通知人

        private final String value;

        private final String code;

        WarringEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (WarringEnum type : WarringEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 海关、立交桥报关单状态映射关系 enum
     */
    public enum Hg_ljq_mapperEnum {
        MAP_1("J", "审单中心确定办理税费及证件手续", "016", "无纸化审结"),
        MAP_2("K", "通关无纸化担保放行", "012", "担保放行"),
        MAP_3("V", "挂起", "014", "需手工申报"),
        MAP_4("P", "放行", "025", "海关已放行"),
        MAP_5("O", "准予进卡", "011", "海关已接收"),
        MAP_6("d", "施检", "103", "施检"),
        MAP_7("b", "施检", "103", "施检"),
        MAP_8("A", "被记录报;报关单放行前删除或者异常处理;", "020", "被记录报;报关单放行前删除或者异常处理;"),
        MAP_9("2", "QP接收成功，上载发往数据中心", "003", "QP接收成功，上载发往数据中心"),
        MAP_10("6", "QP接收成功，申报发往数据中心", "004", "QP接收成功，申报发往数据中心"),
        MAP_11("N", "重传文件", "101", "重传文件"),
        MAP_12("3", "上载成功", "006", "上载成功"),
        MAP_13("4", "上载失败", "007", "上载失败"),
        MAP_14("7", "申报成功", "008", "申报成功"),
        MAP_15("8", "上载未申报（无申报权限）", "009", "上载未申报（无申报权限）"),
        MAP_16("9", "上载未申报（与数据中心联系）", "102", "上载未申报（与数据中心联系）"),
        MAP_17("L", "海关已接收", "011", "海关已接收"),
        MAP_18("B", "担保放行", "012", "担保放行"),
        MAP_19("E", "不被受理", "013", "不被受理"),
        MAP_20("H", "需手工申报", "014", "需手工申报"),
        MAP_21("Z", "退回修改", "015", "退回修改"),
        MAP_22("G", "报关单已审结", "016", "报关单已审结"),
        MAP_23("F", "放行交单", "017", "放行交单"),
        MAP_24("T", "需交税费", "018", "需交税费"),
        MAP_25("Y", "申报失败", "019", "申报失败"),
        MAP_26("D", "海关删单", "020", "海关删单"),
        MAP_27("M", "报关单重审", "021", "报关单重审"),
        MAP_28("R", "已结关", "022", "已结关"),
        MAP_29("W", "进出口审结/查验/放行通知", "023", "进出口审结/查验/放行通知"),
        MAP_30("L", "进出口审结/查验/放行通知", "023", "进出口审结/查验/放行通知"),
        MAP_31("C", "查验通知", "024", "查验通知"),
        MAP_32("S", "施检", "103", "施检"),
        MAP_33("a", "签证", "104", "签证"),
        MAP_34("W", "进出口审结/查验/放行通知", "023", "进出口审结/查验/放行通知"),
        MAP_35("L", "进出口审结/查验/放行通知", "023", "进出口审结/查验/放行通知"),
        MAP_36("1", "要求补充申报通知", "029", "要求补充申报通知"),
        MAP_37("c", "检验检疫撤单", "020", "海关删单"),
        MAP_38("0", "(0);准予拟证 ", "025", "海关已放行"),
        ;

        private String hgCode;
        private String hgName;
        private String ljqCode;
        private String ljqName;

        Hg_ljq_mapperEnum(String hgCode, String hgName, String ljqCode, String ljqName) {
            this.hgCode = hgCode;
            this.hgName = hgName;
            this.ljqCode = ljqCode;
            this.ljqName = ljqName;
        }

        static String getLjqCodeByhgCode(String hgCode) {
            Hg_ljq_mapperEnum[] arr = Hg_ljq_mapperEnum.values();
            for (Hg_ljq_mapperEnum enumElmt : arr) {
                if (StringUtils.equals(enumElmt.getHgCode(), hgCode)) {
                    return enumElmt.getLjqCode();
                }
            }
            return null;
        }

        public static String getHgCodeByLjqCode(String ljqCode) {
            Hg_ljq_mapperEnum[] arr = Hg_ljq_mapperEnum.values();
            for (Hg_ljq_mapperEnum enumElmt : arr) {
                if (StringUtils.equals(enumElmt.getLjqCode(), ljqCode)) {
                    return enumElmt.getHgCode();
                }
            }
            return null;
        }

        public static String getHgNameByHgode(String hgCode) {
            Hg_ljq_mapperEnum[] arr = Hg_ljq_mapperEnum.values();
            for (Hg_ljq_mapperEnum enumElmt : arr) {
                if (StringUtils.equals(enumElmt.getHgCode(), hgCode)) {
                    return enumElmt.getHgName();
                }
            }
            return null;
        }

        public String getHgCode() {
            return hgCode;
        }

        public void setHgCode(String hgCode) {
            this.hgCode = hgCode;
        }

        public String getHgName() {
            return hgName;
        }

        public void setHgName(String hgName) {
            this.hgName = hgName;
        }

        public String getLjqCode() {
            return ljqCode;
        }

        public void setLjqCode(String ljqCode) {
            this.ljqCode = ljqCode;
        }

        public String getLjqName() {
            return ljqName;
        }

        public void setLjqName(String ljqName) {
            this.ljqName = ljqName;
        }

    }

    /**
     * 新旧包装种类对应关系enum
     */
    public enum new_old_wrapType_mapperEnum {
        MAP_1("00", "散装", "4", "散装", "9993", "散装"),
        MAP_2("01", "裸装", "7", "其他", "9994", "裸装"),
        MAP_3("22", "纸质或纤维板制盒/箱", "2", "纸箱", "4M", "纸箱"),
        MAP_4("23", "木制或竹藤等植物性材料制盒/箱", "1", "木箱", "4C11", "木制箱"),
        MAP_5("29", "其他材料制盒/箱", "7", "其他", "490", "其他箱"),
        MAP_6("32", "纸质或纤维板制桶", "3", "桶装", "1G", "纤维圆桶"),
        MAP_7("33", "木制或竹藤等植物性材料制桶", "3", "桶装", "1C", "木制桶"),
        MAP_8("39", "其他材料制桶", "3", "桶装", "190", "其他桶"),
        MAP_9("04", "球状罐类", "7", "其他", "390", "其他罐"),
        MAP_10("06", "包/袋", "6", "包", "590", "包/袋类"),
        MAP_11("92", "再生木托", "5", "托盘", "9F91", "再生托盘"),
        MAP_12("93", "天然木托", "5", "托盘", "9C91", "天然托盘"),
        MAP_13("98", "植物性铺垫材料", "7", "其他", "9992", "植物性铺垫材料"),
        MAP_14("99", "其他包装", "7", "其他", "9999", "其他"),
        ;

        private String newCode;
        private String newName;
        private String oldBGCode;   //原报关代码
        private String oldBGName;   //原报关名称
        private String oldBJCode;   //原报检代码
        private String oldBJName;   //原报检名称

        new_old_wrapType_mapperEnum(String newCode, String newName, String oldBGCode, String oldBGName, String oldBJCode, String oldBJName) {
            this.newCode = newCode;
            this.newName = newName;
            this.oldBGCode = oldBGCode;
            this.oldBGName = oldBGName;
            this.oldBJCode = oldBJCode;
            this.oldBJName = oldBJName;
        }

        static String getNewCodeByOldBGCode(String oldBGCode) {
            new_old_wrapType_mapperEnum[] arr = new_old_wrapType_mapperEnum.values();
            for (new_old_wrapType_mapperEnum enumElmt : arr) {
                if (StringUtils.equals(enumElmt.getOldBGCode(), oldBGCode)) {
                    return enumElmt.getNewCode();
                }
            }
            return null;
        }

        public static String getOldBGCodeByNewCode(String newCode) {
            new_old_wrapType_mapperEnum[] arr = new_old_wrapType_mapperEnum.values();
            for (new_old_wrapType_mapperEnum enumElmt : arr) {
                if (StringUtils.equals(enumElmt.getNewCode(), newCode)) {
                    return enumElmt.getOldBGCode();
                }
            }
            return null;
        }

        public String getNewCode() {
            return newCode;
        }

        public void setNewCode(String newCode) {
            this.newCode = newCode;
        }

        public String getNewName() {
            return newName;
        }

        public void setNewName(String newName) {
            this.newName = newName;
        }

        public String getOldBGCode() {
            return oldBGCode;
        }

        public void setOldBGCode(String oldBGCode) {
            this.oldBGCode = oldBGCode;
        }

        public String getOldBGName() {
            return oldBGName;
        }

        public void setOldBGName(String oldBGName) {
            this.oldBGName = oldBGName;
        }

        public String getOldBJCode() {
            return oldBJCode;
        }

        public void setOldBJCode(String oldBJCode) {
            this.oldBJCode = oldBJCode;
        }

        public String getOldBJName() {
            return oldBJName;
        }

        public void setOldBJName(String oldBJName) {
            this.oldBJName = oldBJName;
        }
    }

    /**
     * 税单下载状态
     */
    public enum DutyFormStatusEnum {
        NOT_EXIST("未下载", "0"),
        DOWNLOADING("下载中", "1"),
        DONE("下载成功", "2"),
        FAILED("下载失败", "3"),
        NOTFOUND("未下载到税单", "4");

        private final String desc;

        private final String status;

        DutyFormStatusEnum(String desc, String status) {
            this.desc = desc;
            this.status = status;
        }

        public String getStatus() {
            return status;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据类型的 code，返回类型的 value
         */
        public static String getDesc(String status) {
            if (StringUtils.isBlank(status)) {
                return NOT_EXIST.getDesc();
            }
            for (DutyFormStatusEnum type : DutyFormStatusEnum.values()) {
                if (type.getStatus().equals(status)) {
                    return type.getDesc();
                }
            }
            return NOT_EXIST.getDesc();
        }
    }

    /**
     * 企业物料贸易类型对应类型
     * A 不限 B 来料加工 C 进料加工
     */
    public enum tModeEnum {
        TMODE_0214("0214", "B"),
        TMODE_0245("0245", "B"),
        TMODE_0255("0255", "B"),
        TMODE_0258("0258", "B"),
        TMODE_0300("0300", "B"),
        TMODE_0345("0345", "B"),
        TMODE_0445("0445", "B"),
        TMODE_0545("0545", "B"),
        TMODE_0845("0845", "B"),
        TMODE_0865("0865", "B"),
        TMODE_4400("4400", "B"),
        TMODE_5014("5014", "B"),
        TMODE_0265("0265", "B"),
        TMODE_0444("0444", "C"),
        TMODE_0544("0544", "C"),
        TMODE_0615("0615", "C"),
        TMODE_0642("0642", "C"),
        TMODE_0644("0644", "C"),
        TMODE_0654("0654", "C"),
        TMODE_0657("0657", "C"),
        TMODE_0700("0700", "C"),
        TMODE_0715("0715", "C"),
        TMODE_0744("0744", "C"),
        TMODE_0844("0844", "C"),
        TMODE_0864("0864", "C"),
        TMODE_4600("4600", "C"),
        TMODE_0664("0664", "C"),
        TMODE_5015("5015", "C");

        private final String value;

        private final String code;

        tModeEnum(String code, String value) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            if (StringUtils.isNotBlank(code)) {
                for (tModeEnum type : tModeEnum.values()) {
                    if (type.getCode().equals(code)) {
                        return type.getValue();
                    }
                }
            }
            return null;
        }
    }

    /**
     * 免表参数名称枚举
     */
    public enum avoidEnum {
        AVOID_PARAMS_0("审批依据", "0"),
        AVOID_PARAMS_1("审批部门", "1"),
        AVOID_PARAMS_2("产业政策审批条目", "2"),
        AVOID_PARAMS_3("业务种类", "3"),
        AVOID_PARAMS_4("申请人种类/代码", "4"),
        AVOID_PARAMS_5("申请人市场主体类型/代码", "5");

        private final String value;

        private final String code;

        avoidEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (avoidEnum type : avoidEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 进出口方式
     */
    public enum InOutWayEnum {
        WAY_1("一线进出口", "1"),
        WAY_2("二线进出口", "2"),
        WAY_3("境外保税", "3"),
        WAY_4("境外非保税", "4"),
        WAY_5("物流园", "5"),
        WAY_6("国内结转", "6"),
        WAY_7("内销", "7");

        private final String value;

        private final String code;

        InOutWayEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (InOutWayEnum type : InOutWayEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (InOutWayEnum type : InOutWayEnum.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 生成发票表体校验字段
     */
    public enum bodyList {
        VOLUME,
        USE_TYPE,
        USD_PRICE,
        UPDATE_USER,
        UPDATE_TIME,
        UNIT_2,
        UNIT_1,
        UNIT,
        SUPPLIER_NAME,
        SUPPLIER_CODE,
        SID,
        SERIAL_NO,
        QTY_2,
        QTY_1,
        QTY,
        ORIGIN_COUNTRY,
        ORDER_NO,
        NOTE,
        NET_WT,
        LINKED_NO,
        INVOICE_NO,
        INSERT_USER,
        INSERT_TIME,
        HEAD_ID,
        G_NO,
        G_NAME,
        G_MODEL,
        G_MARK,
        GROSS_WT,
        FAC_G_NO,
        FACTOR_WT,
        FACTOR_2,
        FACTOR_1,
        EXG_VERSION,
        ENTRY_G_NO,
        EMS_NO,
        EMS_LIST_NO,
        DUTY_MODE,
        DESTINATION_COUNTRY,
        DEC_TOTAL,
        DEC_PRICE,
        CUSTOMER_ORDER_NO,
        CUSTOMER_G_NO,
        CURR,
        COP_G_NO,
        COP_G_NAME,
        COP_G_MODEL,
        COP_EMS_NO,
        CODE_T_S,
        CLASS_MARK,
        BOND_MARK,
        ARRIVAL_DATE,
        NOTE_1,
        NOTE_2,
        NOTE_3,
        COP_G_NAME_EN,
        COP_G_MODEL_EN,
        TRADE_MODE,
        DISTRICT_CODE,
        DISTRICT_POST_CODE,
        COST_CENTER,
        LINE_NO,
        ITEM_NO,
        BUYER,
        BILL_G_NO,
        IN_OUT_NO,
        CIQ_NO,
        FS_LIST_NO,
        APPLY_NO,
        SINGLE_WEIGHT,
        CONFIRM_ROYALTIES,
        CONFIRM_ROYALTIES_NO,
        ENTRY_NO,
        CUT_MODE,
        CLIENT_CODE,
        CLIENT_NAME,
        BOX_LENGTH,
        BOX_VOLUME,
        BOX_NET_WT,
        BOX_GROSS_WT,
        BOX_CARTON_QTY,
        BOX_CARTON_UNIT,
        BOX_PALLET_NUM,
        CONTAINER_MD,
        MAT_NOTE,
        PALLET_LENGTH,
        BOX_LENGTH_PLTS,
        PALLET_LENGTH_PLTS,
        ROW_NO_ALL,
        BOX_CARTON_NUM,
        BOX_PALLET_QTY,
        G_MODEL_ALL,
        QTY_PCS,
        FAC_G_NO_ALL,
        FAC_G_NO_LIST,
        CLIENT_PRICE,
        PALLET_PLTS,
        BCI_LENGTH,
        CLIENT_TOTAL,
        PAY_TOTAL,
        MAT_CLIENT_PRICE,
        MAT_CLIENT_TOTAL,
        ORDER_LINE_NO,
        PACK_NUM,
        CARTON_NO,
        CARTON_NUM,
        PALLET_NO,
        PALLET_NUM,
        PACKING_GROSS_WT,
        PACKING_VOLUME,
        CARTON_MODEL,
        WRAP_TYPE,
        IPPC,
        PACKING_LIST,
        SO_NO,
        PACKING_CUSTOMER_ORDER_NO,
        PACKING_CUSTOMER_G_NO,
        PACKING_FAC_G_NO,
        PACKING_QTY,
        PACKING_NET_WT,
        PACKING_BATCH_NO,
        BATCH_NO,
        PACKING_UNIT,
        LIST_CARTON_NUM
    }

    /**
     * 报关单通道
     * 1 金二上载单一、2 立交桥上载单一、3立交桥暂存
     * 转换关系：
     * 1.金二上载单一：    send_dest=1，   general_ent=1
     * 2.立交桥上载单一：  send_dest=0，   general_ent=1
     * 3.立交桥暂存：      send_dest=0，   general_ent=0
     */
    public enum entryChannelType {
        ENTRY_CHANNEL_1("1", "1", "1"),
        ENTRY_CHANNEL_2("2", "0", "1"),
        ENTRY_CHANNEL_3("3", "0", "0");

        private final String code;

        public String getCode() {
            return this.code;
        }

        private final String sendDest;

        public String getSendDest() {
            return this.sendDest;
        }

        private final String generalEnt;

        public String getGeneralEnt() {
            return this.generalEnt;
        }

        entryChannelType(String code, String sendDest, String generalEnt) {
            this.code = code;
            this.sendDest = sendDest;
            this.generalEnt = generalEnt;
        }

        public static String getSendDest(String code) {
            for (entryChannelType type : entryChannelType.values()) {
                if (type.getCode().equals(code)) {
                    return type.getSendDest();
                }
            }
            return "";
        }

        public static String getGeneralEnt(String code) {
            for (entryChannelType type : entryChannelType.values()) {
                if (type.getCode().equals(code)) {
                    return type.getGeneralEnt();
                }
            }
            return "";
        }

        public static String getCode(String sendDest, String generalEnt) {
            for (entryChannelType type : entryChannelType.values()) {
                if (type.getSendDest().equals(sendDest) && type.getGeneralEnt().equals(generalEnt)) {
                    return type.getCode();
                }
            }
            return "";
        }


    }

    /**
     * 提取状态 0:未提取,1:已提取
     */
    public enum takeStatusEnum {
        STATUS_0("未提取", "0"),
        STATUS_1("已提取", "1");

        private final String value;

        private final String code;

        takeStatusEnum(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (takeStatusEnum type : takeStatusEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 料件/成品出入库 提取状态 0:未提取,1:分送集报,2:深加工,3:内销
     */
    public enum IMG_EXG_STATUS {
        STATUS_0("未提取", "0"),
        STATUS_1("分送集报", "1"),
        STATUS_2("深加工", "2"),
        STATUS_3("内销", "3");

        private final String value;

        private final String code;

        IMG_EXG_STATUS(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (IMG_EXG_STATUS type : IMG_EXG_STATUS.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 证书控制类型
     * <p>
     * 0 不控次不控量      timesCtrlFlag = 0，    qtyCtrlFlag = 0
     * 1 仅控次            timesCtrlFlag = 1，    qtyCtrlFlag = 0
     * 2 仅控表头数量      timesCtrlFlag = 0，    qtyCtrlFlag = 1
     * 3 仅控表体数量      timesCtrlFlag = 0，    qtyCtrlFlag = 2
     * 4 控次、控表头数量  timesCtrlFlag = 1，    qtyCtrlFlag = 1
     * 5 控次、控表体数量  timesCtrlFlag = 1，    qtyCtrlFlag = 2
     */
    public enum certCtrlType {
        CTRL_TYPE_NONE("0", "0", "0"),
        CTRL_TYPE_ONLY_TIMES("1", "1", "0"),
        CTRL_TYPE_ONLY_HEAD_QTY("2", "0", "1"),
        CTRL_TYPE_ONLY_LIST_QTY("3", "0", "2"),
        CTRL_TYPE_TIMES_AND_HEAD_QTY("4", "1", "1"),
        CTRL_TYPE_TIMES_AND_LIST_QTY("5", "1", "2");

        private final String ctrlType;
        private final String timesCtrlFlag;
        private final String qtyCtrlFlag;

        public String getCtrlType() {
            return this.ctrlType;
        }

        public String getTimesCtrlFlag() {
            return this.timesCtrlFlag;
        }

        public String getQtyCtrlFlag() {
            return this.qtyCtrlFlag;
        }

        certCtrlType(String ctrlType, String timesCtrlFlag, String qtyCtrlFlag) {
            this.ctrlType = ctrlType;
            this.timesCtrlFlag = timesCtrlFlag;
            this.qtyCtrlFlag = qtyCtrlFlag;
        }

        public static String getCtrlType(String timesCtrlFlag, String qtyCtrlFlag) {
            for (certCtrlType type : certCtrlType.values()) {
                if (type.getTimesCtrlFlag().equals(timesCtrlFlag) && type.getQtyCtrlFlag().equals(qtyCtrlFlag)) {
                    return type.getCtrlType();
                }
            }
            return "";
        }

        public static String getTimesCtrlFlag(String ctrlType) {
            for (certCtrlType type : certCtrlType.values()) {
                if (type.getCtrlType().equals(ctrlType)) {
                    return type.getTimesCtrlFlag();
                }
            }
            return "";
        }

        public static String getQtyCtrlFlag(String ctrlType) {
            for (certCtrlType type : certCtrlType.values()) {
                if (type.getCtrlType().equals(ctrlType)) {
                    return type.getQtyCtrlFlag();
                }
            }
            return "";
        }
    }

    /**
     * 移动类型 0:成品入库, 1:成品出库
     */
    public enum EXG_TYPE_ENUM {
        EXG_0("成品入库", "0"),
        EXG_1("成品出库", "1");

        private final String value;

        private final String code;

        EXG_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (EXG_TYPE_ENUM type : EXG_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 移动类型 0:料件入库,1:料件出库
     */
    public enum IMG_TYPE_ENUM {
        IMG_0("料件入库", "0"),
        IMG_1("料件出库", "1");

        private final String value;

        private final String code;

        IMG_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (IMG_TYPE_ENUM type : IMG_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 发票、箱单类型
     */
    public enum InvoiceBoxType {
        /**
         * 发票
         */
        Invoice("发票", "1"),
        /**
         * 箱单
         */
        Box("箱单", "2"),
        /**
         * 合同
         */
        Contract("合同", "3"),
        Trust("托书", "4"),
        Booking("订舱单", "5");
        private final String value;
        private final String code;

        InvoiceBoxType(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return this.code;
        }

        public String getValue() {
            return this.value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (InvoiceBoxType type : InvoiceBoxType.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 支付状态 N:未支付，S:已支付，P:支付中
     */
    public enum TRANS_STATUS {
        TRANS_STATUS_N("未支付", "N"),
        TRANS_STATUS_S("已支付", "S"),
        TRANS_STATUS_P("支付中", "P"),
        TRANS_STATUS_A("支付中", "A"),

        TRANS_STATUS_X("支付失败", "X");

        private final String value;

        private final String code;

        TRANS_STATUS(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (TRANS_STATUS type : TRANS_STATUS.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 税费种类
     */
    public enum TAX_TYPE {
        TAX_TYPE_A("进口关税", "A"),
        TAX_TYPE_B("112项关税", "B"),
        TAX_TYPE_C("出口关税", "C"),
        TAX_TYPE_D("进口农产税", "D"),
        TAX_TYPE_E("特点关税", "E"),
        TAX_TYPE_F("112对台税", "F"),
        TAX_TYPE_G("直接对台", "G"),
        TAX_TYPE_H("特定增值税", "H"),
        TAX_TYPE_I("反倾销税", "I"),
        TAX_TYPE_J("反补贴税", "J"),
        TAX_TYPE_L("进口增值税", "L"),
        TAX_TYPE_M("罚没收入", "M"),
        TAX_TYPE_N("关税缓息", "N"),
        TAX_TYPE_O("进口废弃电器电子产品处理基金", "O"),
        TAX_TYPE_P("消费税缓息", "P"),
        TAX_TYPE_Q("增值税缓息", "Q"),
        TAX_TYPE_R("全额退关税", "R"),
        TAX_TYPE_S("监管手续费", "S"),
        TAX_TYPE_U("滞报金", "U"),
        TAX_TYPE_V("滞纳金", "V"),
        TAX_TYPE_W("行李物品", "W"),
        TAX_TYPE_X("邮递物品", "X"),
        TAX_TYPE_Y("进口消费税", "Y"),
        TAX_TYPE_Z("关务费", "Z"),
        TAX_TYPE_("规费", "[");

        private final String value;

        private final String code;

        TAX_TYPE(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (TAX_TYPE type : TAX_TYPE.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 进口监管方式
     * 非保税校验强制用A类型
     */
    public enum tradeModeEnumI {
        TRADEMODE_0200("0200", "B"),
        TRADEMODE_0214("0214", "B"),
        TRADEMODE_0245("0245", "B"),
        TRADEMODE_0255("0255", "B"),
        TRADEMODE_0258("0258", "B"),
        TRADEMODE_0300("0300", "B"),
        TRADEMODE_0314("0314", "B"),
        TRADEMODE_0320("0320", "B"),
        TRADEMODE_0345("0345", "B"),
        TRADEMODE_0445("0445", "B"),
        TRADEMODE_0545("0545", "B"),
        TRADEMODE_0845("0845", "B"),
        TRADEMODE_0865("0865", "B"),
        TRADEMODE_4400("4400", "B"),
        TRADEMODE_5014("5014", "B"),
        TRADEMODE_9700("9700", "B"),
        TRADEMODE_1371("1371", "B"),
        TRADEMODE_AAAAB("AAAA", "B"),
        TRADEMODE_0400B("0400", "B"),
        TRADEMODE_0200C("0200", "C"),
        TRADEMODE_0444("0444", "C"),
        TRADEMODE_0544("0544", "C"),
        TRADEMODE_0615("0615", "C"),
        TRADEMODE_0642("0642", "C"),
        TRADEMODE_0644("0644", "C"),
        TRADEMODE_0654("0654", "C"),
        TRADEMODE_0657("0657", "C"),
        TRADEMODE_0700("0700", "C"),
        TRADEMODE_0715("0715", "C"),
        TRADEMODE_0744("0744", "C"),
        TRADEMODE_0815("0815", "C"),
        TRADEMODE_0844("0844", "C"),
        TRADEMODE_0864("0864", "C"),
        TRADEMODE_4600("4600", "C"),
        TRADEMODE_1215("1215", "C"),
        TRADEMODE_5015("5015", "C"),
        TRADEMODE_9700C("9700", "C"),
        TRADEMODE_1371C("1371", "C"),
        TRADEMODE_AAAAC("AAAA", "C"),
        TRADEMODE_0400C("0400", "C"),
        TRADEMODE_0400E("0400", "E"),
        TRADEMODE_0200E("0200", "E"),
        TRADEMODE_0214E("0214", "E"),
        TRADEMODE_0245E("0245", "E"),
        TRADEMODE_0255E("0255", "E"),
        TRADEMODE_0258E("0258", "E"),
        TRADEMODE_0300E("0300", "E"),
        TRADEMODE_0314E("0314", "E"),
        TRADEMODE_0320E("0320", "E"),
        TRADEMODE_0345E("0345", "E"),
        TRADEMODE_0444E("0444", "E"),
        TRADEMODE_0445E("0445", "E"),
        TRADEMODE_0544E("0544", "E"),
        TRADEMODE_0545E("0545", "E"),
        TRADEMODE_0615E("0615", "E"),
        TRADEMODE_0642E("0642", "E"),
        TRADEMODE_0644E("0644", "E"),
        TRADEMODE_0654E("0654", "E"),
        TRADEMODE_0657E("0657", "E"),
        TRADEMODE_0700E("0700", "E"),
        TRADEMODE_0715E("0715", "E"),
        TRADEMODE_0744E("0744", "E"),
        TRADEMODE_0815E("0815", "E"),
        TRADEMODE_0844E("0844", "E"),
        TRADEMODE_0845E("0845", "E"),
        TRADEMODE_0864E("0864", "E"),
        TRADEMODE_0865E("0865", "E"),
        TRADEMODE_4400E("4400", "E"),
        TRADEMODE_4600E("4600", "E"),
        TRADEMODE_5014E("5014", "E"),
        TRADEMODE_1215E("1215", "E"),
        TRADEMODE_5015E("5015", "E"),
        TRADEMODE_9700E("9700", "E"),
        TRADEMODE_1371E("1371", "E"),
        TRADEMODE_AAAAE("AAAA", "E"),
        TRADEMODE_0110("0110", "A"),
        TRADEMODE_0130("0130", "A"),
        TRADEMODE_0139("0139", "A"),
        TRADEMODE_0200N("0200", "A"),
        TRADEMODE_0265N("0265", "A"),
        TRADEMODE_0400("0400", "A"),
        TRADEMODE_0420("0420", "A"),
        TRADEMODE_0446("0446", "A"),
        TRADEMODE_0456("0456", "A"),
        TRADEMODE_0466("0466", "A"),
        TRADEMODE_0500("0500", "A"),
        TRADEMODE_0513("0513", "A"),
        TRADEMODE_0664N("0664", "A"),
        TRADEMODE_1039("1039", "A"),
        TRADEMODE_1139("1139", "A"),
        TRADEMODE_1200("1200", "A"),
        TRADEMODE_1210("1210", "A"),
        TRADEMODE_1233("1233", "A"),
        TRADEMODE_1234("1234", "A"),
        TRADEMODE_1239("1239", "A"),
        TRADEMODE_1300("1300", "A"),
        TRADEMODE_1371A("1371", "A"),
        TRADEMODE_1427("1427", "A"),
        TRADEMODE_1500("1500", "A"),
        TRADEMODE_1523("1523", "A"),
        TRADEMODE_1616("1616", "A"),
        TRADEMODE_1741("1741", "A"),
        TRADEMODE_1831("1831", "A"),
        TRADEMODE_2025("2025", "A"),
        TRADEMODE_2210("2210", "A"),
        TRADEMODE_2225("2225", "A"),
        TRADEMODE_2439("2439", "A"),
        TRADEMODE_2600("2600", "A"),
        TRADEMODE_2700("2700", "A"),
        TRADEMODE_2939("2939", "A"),
        TRADEMODE_3010("3010", "A"),
        TRADEMODE_3039("3039", "A"),
        TRADEMODE_3100("3100", "A"),
        TRADEMODE_3339("3339", "A"),
        TRADEMODE_3410("3410", "A"),
        TRADEMODE_3422("3422", "A"),
        TRADEMODE_3511("3511", "A"),
        TRADEMODE_3611("3611", "A"),
        TRADEMODE_3612("3612", "A"),
        TRADEMODE_3910("3910", "A"),
        TRADEMODE_4019("4019", "A"),
        TRADEMODE_4039("4039", "A"),
        TRADEMODE_4139("4139", "A"),
        TRADEMODE_4200("4200", "A"),
        TRADEMODE_4239("4239", "A"),
        TRADEMODE_4500("4500", "A"),
        TRADEMODE_4539("4539", "A"),
        TRADEMODE_4561("4561", "A"),
        TRADEMODE_5000("5000", "A"),
        TRADEMODE_5010("5010", "A"),
        TRADEMODE_5033("5033", "A"),
        TRADEMODE_5034("5034", "A"),
        TRADEMODE_5100("5100", "A"),
        TRADEMODE_5200("5200", "A"),
        TRADEMODE_5300("5300", "A"),
        TRADEMODE_5335("5335", "A"),
        TRADEMODE_5361("5361", "A"),
        TRADEMODE_6033("6033", "A"),
        TRADEMODE_9500("9500", "A"),
        TRADEMODE_9600("9600", "A"),
        TRADEMODE_9610("9610", "A"),
        TRADEMODE_9639("9639", "A"),
        TRADEMODE_9700A("9700", "A"),
        TRADEMODE_9739("9739", "A"),
        TRADEMODE_9800("9800", "A"),
        TRADEMODE_9839("9839", "A"),
        TRADEMODE_9900("9900", "A"),
        TRADEMODE_9710("9710", "A"),
        TRADEMODE_9810("9810", "A"),
        TRADEMODE_BBBB("BBBB", "A");

        private final String tradeModeCode;

        private final String tradeEmsNo;

        tradeModeEnumI(String tradeModeCode, String tradeEmsNo) {
            this.tradeModeCode = tradeModeCode;
            this.tradeEmsNo = tradeEmsNo;
        }

        public String getTradeModeCode() {
            return tradeModeCode;
        }

        public String getTradeEmsNo() {
            return tradeEmsNo;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param tradeModeCode 类型code
         */
        public static String getValue(String tradeModeCode) {
            for (tradeModeEnumI type : tradeModeEnumI.values()) {
                if (type.getTradeModeCode().equals(tradeModeCode)) {
                    return type.getTradeEmsNo();
                }
            }
            return "";
        }

        public static boolean checkCode(String tradeModeCode, String tradeEmsNo) {
            for (tradeModeEnumI type : tradeModeEnumI.values()) {
                if (type.getTradeModeCode().equals(tradeModeCode) && type.getTradeEmsNo().equals(tradeEmsNo)) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 出口监管方式
     * 非保税校验强制用A类型
     */
    public enum tradeModeEnumE {
        TRADEMODE_0200("0200", "B"),
        TRADEMODE_0214("0214", "B"),
        TRADEMODE_0255("0255", "B"),
        TRADEMODE_0258("0258", "B"),
        TRADEMODE_0265("0265", "B"),
        TRADEMODE_0300("0300", "B"),
        TRADEMODE_0314("0314", "B"),
        TRADEMODE_0320("0320", "B"),
        TRADEMODE_0345("0345", "B"),
        TRADEMODE_0445("0445", "B"),
        TRADEMODE_0545("0545", "B"),
        TRADEMODE_0845("0845", "B"),
        TRADEMODE_0865("0865", "B"),
        TRADEMODE_4400("4400", "B"),
        TRADEMODE_5014("5014", "B"),
        TRADEMODE_1371("1371", "B"),
        TRADEMODE_AAAAB("AAAA", "B"),
        TRADEMODE_0400B("0400", "B"),
        TRADEMODE_0200C("0200", "C"),
        TRADEMODE_0444("0444", "C"),
        TRADEMODE_0544("0544", "C"),
        TRADEMODE_0615("0615", "C"),
        TRADEMODE_0642("0642", "C"),
        TRADEMODE_0654("0654", "C"),
        TRADEMODE_0657("0657", "C"),
        TRADEMODE_0664("0664", "C"),
        TRADEMODE_0700("0700", "C"),
        TRADEMODE_0715("0715", "C"),
        TRADEMODE_0744("0744", "C"),
        TRADEMODE_0815("0815", "C"),
        TRADEMODE_0844("0844", "C"),
        TRADEMODE_0864("0864", "C"),
        TRADEMODE_4600("4600", "C"),
        TRADEMODE_1215("1215", "C"),
        TRADEMODE_5015("5015", "C"),
        TRADEMODE_1371C("1371", "C"),
        TRADEMODE_AAAAC("AAAA", "C"),
        TRADEMODE_0400C("0400", "C"),
        TRADEMODE_0200E("0200", "E"),
        TRADEMODE_0214E("0214", "E"),
        TRADEMODE_0255E("0255", "E"),
        TRADEMODE_0258E("0258", "E"),
        TRADEMODE_0265E("0265", "E"),
        TRADEMODE_0300E("0300", "E"),
        TRADEMODE_0314E("0314", "E"),
        TRADEMODE_0320E("0320", "E"),
        TRADEMODE_0345E("0345", "E"),
        TRADEMODE_0444E("0444", "E"),
        TRADEMODE_0445E("0445", "E"),
        TRADEMODE_0544E("0544", "E"),
        TRADEMODE_0545E("0545", "E"),
        TRADEMODE_0615E("0615", "E"),
        TRADEMODE_0642E("0642", "E"),
        TRADEMODE_0644E("0644", "E"),
        TRADEMODE_0654E("0654", "E"),
        TRADEMODE_0657E("0657", "E"),
        TRADEMODE_0664E("0664", "E"),
        TRADEMODE_0700E("0700", "E"),
        TRADEMODE_0715E("0715", "E"),
        TRADEMODE_0744E("0744", "E"),
        TRADEMODE_0815E("0815", "E"),
        TRADEMODE_0844E("0844", "E"),
        TRADEMODE_0845E("0845", "E"),
        TRADEMODE_0864E("0864", "E"),
        TRADEMODE_0865E("0865", "E"),
        TRADEMODE_4400E("4400", "E"),
        TRADEMODE_4600E("4600", "E"),
        TRADEMODE_5014E("5014", "E"),
        TRADEMODE_1215E("1215", "E"),
        TRADEMODE_5015E("5015", "E"),
        TRADEMODE_1371E("1371", "E"),
        TRADEMODE_AAAAE("AAAA", "E"),
        TRADEMODE_0400E("0400", "E"),
        TRADEMODE_0110("0110", "A"),
        TRADEMODE_0130("0130", "A"),
        TRADEMODE_0139("0139", "A"),
        TRADEMODE_0200N("0200", "A"),
        TRADEMODE_0245N("0245", "A"),
        TRADEMODE_0400("0400", "A"),
        TRADEMODE_0420("0420", "A"),
        TRADEMODE_0446("0446", "A"),
        TRADEMODE_0456("0456", "A"),
        TRADEMODE_0466("0466", "A"),
        TRADEMODE_0500("0500", "A"),
        TRADEMODE_0513("0513", "A"),
        TRADEMODE_0644N("0644", "A"),
        TRADEMODE_1039("1039", "A"),
        TRADEMODE_1139("1139", "A"),
        TRADEMODE_1200("1200", "A"),
        TRADEMODE_1210("1210", "A"),
        TRADEMODE_1233("1233", "A"),
        TRADEMODE_1234("1234", "A"),
        TRADEMODE_1239("1239", "A"),
        TRADEMODE_1300("1300", "A"),
        TRADEMODE_1371A("1371", "A"),
        TRADEMODE_1427("1427", "A"),
        TRADEMODE_1500("1500", "A"),
        TRADEMODE_1523("1523", "A"),
        TRADEMODE_1616("1616", "A"),
        TRADEMODE_1741("1741", "A"),
        TRADEMODE_1831("1831", "A"),
        TRADEMODE_2025("2025", "A"),
        TRADEMODE_2210("2210", "A"),
        TRADEMODE_2225("2225", "A"),
        TRADEMODE_2439("2439", "A"),
        TRADEMODE_2600("2600", "A"),
        TRADEMODE_2700("2700", "A"),
        TRADEMODE_2939("2939", "A"),
        TRADEMODE_3010("3010", "A"),
        TRADEMODE_3039("3039", "A"),
        TRADEMODE_3100("3100", "A"),
        TRADEMODE_3339("3339", "A"),
        TRADEMODE_3410("3410", "A"),
        TRADEMODE_3422("3422", "A"),
        TRADEMODE_3511("3511", "A"),
        TRADEMODE_3611("3611", "A"),
        TRADEMODE_3612("3612", "A"),
        TRADEMODE_3910("3910", "A"),
        TRADEMODE_4019("4019", "A"),
        TRADEMODE_4039("4039", "A"),
        TRADEMODE_4139("4139", "A"),
        TRADEMODE_4200("4200", "A"),
        TRADEMODE_4239("4239", "A"),
        TRADEMODE_4500("4500", "A"),
        TRADEMODE_4539("4539", "A"),
        TRADEMODE_4561("4561", "A"),
        TRADEMODE_5000("5000", "A"),
        TRADEMODE_5010("5010", "A"),
        TRADEMODE_5033("5033", "A"),
        TRADEMODE_5034("5034", "A"),
        TRADEMODE_5100("5100", "A"),
        TRADEMODE_5200("5200", "A"),
        TRADEMODE_5300("5300", "A"),
        TRADEMODE_5335("5335", "A"),
        TRADEMODE_5361("5361", "A"),
        TRADEMODE_6033("6033", "A"),
        TRADEMODE_9500("9500", "A"),
        TRADEMODE_9600("9600", "A"),
        TRADEMODE_9610("9610", "A"),
        TRADEMODE_9639("9639", "A"),
        TRADEMODE_9700("9700", "A"),
        TRADEMODE_9739("9739", "A"),
        TRADEMODE_9800("9800", "A"),
        TRADEMODE_9839("9839", "A"),
        TRADEMODE_9900("9900", "A"),
        TRADEMODE_9710("9710", "A"),
        TRADEMODE_9810("9810", "A"),
        TRADEMODE_BBBB("BBBB", "A");

        private final String tradeModeCode;

        private final String tradeEmsNo;

        tradeModeEnumE(String tradeModeCode, String tradeEmsNo) {
            this.tradeModeCode = tradeModeCode;
            this.tradeEmsNo = tradeEmsNo;
        }

        public String getTradeModeCode() {
            return tradeModeCode;
        }

        public String getTradeEmsNo() {
            return tradeEmsNo;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param tradeModeCode 类型code
         */
        public static String getValue(String tradeModeCode) {
            for (tradeModeEnumE type : tradeModeEnumE.values()) {
                if (type.getTradeModeCode().equals(tradeModeCode)) {
                    return type.getTradeEmsNo();
                }
            }
            return "";
        }

        public static boolean checkCode(String tradeModeCode, String tradeEmsNo) {
            for (tradeModeEnumE type : tradeModeEnumE.values()) {
                if (type.getTradeModeCode().equals(tradeModeCode) && type.getTradeEmsNo().equals(tradeEmsNo)) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 退运表头状态
     */
    public enum HEAD_STATUS_ENUM {
        STATUS_0("暂存", "0"),
        STATUS_1("已匹配完成", "1"),
        STATUS_2("已生成报关", "2");

        private final String value;

        private final String code;

        HEAD_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (HEAD_STATUS_ENUM type : HEAD_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 退运表体状态
     */
    public enum LIST_STATUS_ENUM {
        STATUS_0("未匹配", "0"),
        STATUS_1("已匹配", "1");

        private final String value;

        private final String code;

        LIST_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (LIST_STATUS_ENUM type : LIST_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 1.进料成品退换 2.非保成品退运 3.修理物品 4.非保成品退运(退运货物) 5.料件复出
     */
    public enum RETURN_TYPE_ENUM {
        RETURN_TYPE_1("1 进料成品退换", "1"),
        RETURN_TYPE_2("2 非保成品退运", "2"),
        RETURN_TYPE_3("3 修理物品", "3"),
        RETURN_TYPE_4("4 非保成品退运(退运货物)", "4"),
        RETURN_TYPE_5("5 料件复出", "5");

        private final String value;

        private final String code;

        RETURN_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (RETURN_TYPE_ENUM type : RETURN_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 维护状态 0 未预估 1 已预估 2 已修改
     */
    public enum MAINTAIN_STATUS_ENUM {
        M_STATUS_0("0 未预估", "0"),
        M_STATUS_1("1 已预估", "1"),
        M_STATUS_2("2 已修改", "2");

        private final String value;

        private final String code;

        MAINTAIN_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (MAINTAIN_STATUS_ENUM type : MAINTAIN_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 实际费用维护状态 0 未维护 1 已维护
     */
    public enum COST_STATUS_ENUM {
        M_STATUS_0("0 未维护", "0"),
        M_STATUS_1("1 已维护", "1");

        private final String value;

        private final String code;

        COST_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (COST_STATUS_ENUM type : COST_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 科目种类 1 国外段 2 国际段 3 国内段 4 其他 5 非固定科目 6 快件 GW001 查验费计费种类 GW002 仓储费计费种类
     */
    public enum COURSE_TYPE_ENUM {
        COURSE_TYPE_1("1 国外段", "1"),
        COURSE_TYPE_2("2 国际段", "2"),
        COURSE_TYPE_3("3 国内段", "3"),
        COURSE_TYPE_4("4 其他", "4"),
        COURSE_TYPE_5("5 非固定科目", "5"),
        COURSE_TYPE_6("6 快件", "6"),
        COURSE_TYPE_GW001("GW001 查验费计费种类", "GW001"),
        COURSE_TYPE_GW002("GW002 仓储费计费种类", "GW002");

        private final String value;

        private final String code;

        COURSE_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (COURSE_TYPE_ENUM type : COURSE_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum OCR_STATUS_ENUM {
        OCR_HEAD_STATUS_1("1", "1 已识别"),
        OCR_HEAD_STATUS_2("2", "2 已生成");

        private final String value;
        private final String code;

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        OCR_STATUS_ENUM(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValue(String code) {
            for (OCR_STATUS_ENUM type : OCR_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum OCR_MODIFY_STATUS_ENUM {
        OCR_HEAD_MODIFY_STATUS_1("1", "1 未修改"),
        OCR_HEAD_MODIFY_STATUS_2("2", "2 已修改");

        private final String value;
        private final String code;

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        OCR_MODIFY_STATUS_ENUM(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValue(String code) {
            for (OCR_MODIFY_STATUS_ENUM type : OCR_MODIFY_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum OCR_DATA_TYPE_ENUM {
        OCR_DATA_TYPE_INVOICE("INVOICE", "INVOICE 发票"),
        OCR_DATA_TYPE_PACK("PACKING", "PACKING 箱单"),
        OCR_DATA_TYPE_INVPACK("INV_PACK", "INV_PACK 发票箱单"),
        OCR_DATA_TYPE_LADING("LADING", "LADING 提单");


        private final String value;
        private final String code;

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        OCR_DATA_TYPE_ENUM(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValue(String code) {
            for (OCR_DATA_TYPE_ENUM type : OCR_DATA_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum TOBE_DATA_STATUS_ENUM {
        OCR_DATA_STATUS_0("0 未流转", "0"),
        OCR_DATA_STATUS_PURCHASE("-1 采购退回", "-1"),
        OCR_DATA_STATUS_FINANCE("-2 财务退回", "-2"),
        OCR_DATA_STATUS_4("4 已过滤", "4"),
        OCR_DATA_STATUS_1("1 已流转采购", "1"),
        OCR_DATA_STATUS_5("5 已流转财务", "5");

        private final String value;
        private final String code;

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        TOBE_DATA_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public static String getValue(String code) {
            for (TOBE_DATA_STATUS_ENUM type : TOBE_DATA_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 进出口标志
     */
    public enum CONFIRM_ENUM {
        CONFIRM_0("0 未确认", "0"),
        CONFIRM_1("1 已确认", "1");

        private final String value;

        private final String code;

        CONFIRM_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (CONFIRM_ENUM type : CONFIRM_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 中达OCR字段描述
     */
    public enum DELTA_OCR_FILED_DESC {
        DELTA_OCR_EN_NO("enNo", "EN号码"),
        DELTA_OCR_SHIP_TO("shipTo", "SHIP TO"),
        DELTA_OCR_CONSIGNEE("consignee", "收货人"),
        DELTA_OCR_NOTIFY_PARTY("notifyParty", "通知人"),
        DELTA_OCR_DESP_PORT("despPort", "启运港"),
        DELTA_OCR_MARK_NO("markNo", "标记唛码"),
        DELTA_OCR_GROSS_WT("grossWt", "总毛重"),
        DELTA_OCR_VOLUME("volume", "总体积"),
        DELTA_OCR_OUTER_PACKAGING("outerPackaging", "外包装数量"),
        DELTA_OCR_G_NAME("gName", "主要品名");

        private final String code;
        private final String value;

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        DELTA_OCR_FILED_DESC(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValue(String code) {
            for (DELTA_OCR_FILED_DESC type : DELTA_OCR_FILED_DESC.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * kafka消息类型枚举
     */
    public enum KAFKA_MSG_ENUM {
        BILL_HEAD_DATA("清单表头信息","kafkaBillHeadService", "BILL_HEAD_DATA"),
        ERP_MAT_IMGEXG("物料信息","erpMatImgexg4MqService","ERP_MAT_IMGEXG"),
        ERP_DECI_HEAD("进口表头","erpDecIHead4MqService","ERP_DECI_HEAD"),
        ERP_DECI_LIST("进口表体","erpDecIHead4MqService","ERP_DECI_LIST"),
        ERP_DECI_HEAD_LIST("进口整单","erpDecIHead4MqService","ERP_DECI_HEAD_LIST"),
        ERP_DECE_HEAD("出口表头","erpDecEHead4MqService","ERP_DECE_HEAD"),
        ERP_DECE_LIST("出口表体","erpDecEHead4MqService","ERP_DECE_LIST"),
        ERP_DECE_HEAD_LIST("出口整单","erpDecEHead4MqService","ERP_DECE_HEAD_LIST"),
        ERP_SUPPLIER_CODE("基础资料信息","erpSupplier4MqService","ERP_SUPPLIER_CODE"),
        ERP_IMG_IE_LIST("料件出入库","erpImgIeList4MqService","ERP_IMG_IE_LIST"),
        ERP_EXG_IE_LIST("成品出入库","erpExgIeList4MqService","ERP_EXG_IE_LIST"),
        ERP_E_PLAN_HEAD("出口计划表头","erpDecEPlanHead4MqService","ERP_E_PLAN_HEAD"),
        ERP_E_PLAN_LIST("出口计划表体","erpDecEPlanHead4MqService","ERP_E_PLAN_LIST"),
        ERP_E_PLAN_HEAD_LIST("出口计划整单","erpDecEPlanHead4MqService","ERP_E_PLAN_HEAD_LIST"),
        ERP_CAV_BOM("核销BOM","cavBom4MqService","ERP_CAV_BOM"),
        ERP_MRP_BOM("内销BOM","mrpBom4MqService","ERP_DOMESTIC_BOM"),
        ERP_ORIGINAL_BOM("原始BOM","erpOriginalBom4MqService","ERP_ORIGINAL_BOM"),
        ERP_TAX_CIP("客户对账单(生益定制)","erpTaxCip4MqService","ERP_TAX_CIP"),

        BILL_STATUS("清单状态","kafkaReturnInfoService","BILL_STATUS"),
        ENTRY_STATUS("报关单状态","kafkaEntryStatusService","ENTRY_STATUS"),
        ENTRY_DATA("订阅报关单","kafkaEntryService","ENTRY_DATA"),
        BILL_DATA("清单信息","kafkaBillService","BILL_DATA"),
        DUTYFORM_DATA("实际税金","dutyFormKafkaService","DUTYFORM_DATA"),
        DUTYFORM_ATTACH("缴款书","gwstdDutyformFileKafkaService","DUTYFORM_ATTACH"),
        EXEMPTION_RET("减免税回执","devFreeApplyReturnService","EXEMPTION_RET"),

        PRRET_MESSAGE_GW("外发加工","kafkaPrretMessageService","PRRET_MESSAGE_GW"),
        DHL_RESPONSE_GW("DHL快递下单","kafkaDhlMessageService","DHL_RESPONSE_GW");

        private final String value;
        private final String code;
        private final String serviceName;
        KAFKA_MSG_ENUM(String value,String serviceName,String code) {
            this.value = value;
            this.serviceName = serviceName;
            this.code = code;
        }

        public String getCode() {
            return code;
        }
        public String getValue() {
            return value;
        }
        public String getServiceName() {
            return serviceName;
        }
        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (KAFKA_MSG_ENUM type : KAFKA_MSG_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
        /**
         * 根据类型的code，返回类型的serviceName
         *
         * @param code 类型code
         */
        public static String getServiceName(String code) {
            for (KAFKA_MSG_ENUM type : KAFKA_MSG_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getServiceName();
                }
            }
            return "";
        }
    }

    /**
     * 生成单据类型
     */
    public enum BILL_TYPE_STATUS {
        BILL_TYPE_STATUS_1("发票", "1"),
        BILL_TYPE_STATUS_2("箱单", "2"),
        BILL_TYPE_STATUS_3("合同", "3"),
        BILL_TYPE_STATUS_4("托书", "4");

        private final String value;
        private final String code;

        BILL_TYPE_STATUS(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (BILL_TYPE_STATUS type : BILL_TYPE_STATUS.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 检验监管条件
     */
    public enum INSPECTION_CONDITION {
        INSPECTION_CONDITION_P("P", "进境动植物、动植物产品检疫"),
        INSPECTION_CONDITION_Q("Q", "出境动植物、动植物产品检疫"),
        INSPECTION_CONDITION_V("V", "代表入境卫生检疫"),
        INSPECTION_CONDITION_W("W", "代表出境卫生检疫"),
        INSPECTION_CONDITION_M("M", "进口商品检验"),
        INSPECTION_CONDITION_S("S", "出口食品卫生监督检验"),
        INSPECTION_CONDITION_R("R", "进口食品卫生监督检验"),
        INSPECTION_CONDITION_N("N", "出口商品检验"),
        INSPECTION_CONDITION_L("L", "民用商品入境验证");

        private final String code;
        private final String value;

        INSPECTION_CONDITION(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (INSPECTION_CONDITION type : INSPECTION_CONDITION.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 通关监管条件
     */
    public enum CLEARANCE_CONDITION {
        CLEARANCE_CONDITION_1("1", "进口许可证"),
        CLEARANCE_CONDITION_2("2", "两用物项和技术进口许可证"),
        CLEARANCE_CONDITION_3("3", "两用物项和技术出口许可证"),
        CLEARANCE_CONDITION_4("4", "出口许可证"),
        CLEARANCE_CONDITION_5("5", "纺织品临时出口许可证"),
        CLEARANCE_CONDITION_6("6", "旧机电产品禁止进口"),
        CLEARANCE_CONDITION_7("7", "自动进口许可证"),
        CLEARANCE_CONDITION_8("8", "禁止出口商品"),
        CLEARANCE_CONDITION_9("9", "禁止进口商品"),
        CLEARANCE_CONDITION_A("A", "检验检疫"),
        CLEARANCE_CONDITION_B("B", "电子底账"),
        CLEARANCE_CONDITION_D("D", "出/入境货物通关单（毛坯钻石用"),
        CLEARANCE_CONDITION_E("E", "濒危物种允许出口证明书"),
        CLEARANCE_CONDITION_F("F", "濒危物种允许进口证明书"),
        CLEARANCE_CONDITION_G("G", "两用物项和技术出口许可证(定向)"),
        CLEARANCE_CONDITION_H("H", "港澳OPA纺织品证明"),
        CLEARANCE_CONDITION_I("I", "精神药物进(出)口准许证"),
        CLEARANCE_CONDITION_J("J", "黄金及其制品进出口准许证或批件"),
        CLEARANCE_CONDITION_K("K", "深加工结转申请表"),
        CLEARANCE_CONDITION_L("L", "药品进出口准许证"),
        CLEARANCE_CONDITION_M("M", "密码产品和设备进口许可证"),
        CLEARANCE_CONDITION_O("O", "自动进口许可证(新旧机电产品)"),
        CLEARANCE_CONDITION_P("P", "固体废物进口许可证"),
        CLEARANCE_CONDITION_Q("Q", "进口药品通关单"),
        CLEARANCE_CONDITION_R("R", "进口兽药通关单"),
        CLEARANCE_CONDITION_S("S", "进出口农药登记证明"),
        CLEARANCE_CONDITION_T("T", "银行调运现钞进出境许可证"),
        CLEARANCE_CONDITION_U("U", "合法捕捞产品通关证明"),
        CLEARANCE_CONDITION_W("W", "麻醉药品进出口准许证"),
        CLEARANCE_CONDITION_X("X", "有毒化学品环境管理放行通知单"),
        CLEARANCE_CONDITION_Y("Y", "原产地证明"),
        CLEARANCE_CONDITION_Z("Z", "音像制品进口批准单或节目提取单"),
        CLEARANCE_CONDITION_c("c", "内销征税联系单"),
        CLEARANCE_CONDITION_e("e", "关税配额外优惠税率进口棉花配额"),
        CLEARANCE_CONDITION_q("q", "国别关税配额证明"),
        CLEARANCE_CONDITION_r("r", "预归类标志"),
        CLEARANCE_CONDITION_s("s", "适用ITA税率的商品用途认定证明"),
        CLEARANCE_CONDITION_t("t", "关税配额证明"),
        CLEARANCE_CONDITION_v("v", "自动进口许可证(加工贸易)"),
        CLEARANCE_CONDITION_x("x", "出口许可证(加工贸易)"),
        CLEARANCE_CONDITION_y("y", "出口许可证(边境小额贸易)"),
        CLEARANCE_CONDITION_tt("@", "准予担保通知书"),
        CLEARANCE_CONDITION_a("a", "保税核注清单");

        private final String code;
        private final String value;

        CLEARANCE_CONDITION(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (CLEARANCE_CONDITION type : CLEARANCE_CONDITION.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 生成单据类型
     */
    public enum DEV_FREE_HAS {
        DEV_FREE_HAS_2("需要递交，已递交", "2"),
        DEV_FREE_HAS_3("需要递交，暂未递交", "3"),
        DEV_FREE_HAS_4("不需递交", "4");

        private final String value;
        private final String code;

        DEV_FREE_HAS(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (DEV_FREE_HAS type : DEV_FREE_HAS.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum ENT_TYPE_ENUM {
        ENT_TYPE_ENUM_1("国有", "1"),
        ENT_TYPE_ENUM_2("合作", "2"),
        ENT_TYPE_ENUM_3("合资", "3"),
        ENT_TYPE_ENUM_4("独资", "4"),
        ENT_TYPE_ENUM_5("集体", "5"),
        ENT_TYPE_ENUM_6("私营", "6"),
        ENT_TYPE_ENUM_7("个体工商户", "7"),
        ENT_TYPE_ENUM_8("报关", "8"),
        ENT_TYPE_ENUM_9("其他", "9"),
        ENT_TYPE_ENUM_10("新型显示器件偏光片生产企业", "423g");

        private final String value;
        private final String code;

        ENT_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (ENT_TYPE_ENUM type : ENT_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum BUS_TYPE_ENUM {
        BUS_TYPE_ENUM_1("免表申请", "R"),
        BUS_TYPE_ENUM_2("有限责任公司(非自然人投资或控股的法人独资)", "1153");

        private final String value;
        private final String code;

        BUS_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (BUS_TYPE_ENUM type : BUS_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 国别类型
     */
    public enum COUNTRY_TYPE_ENUM {
        ENT_TYPE_ENUM_1("DHL", "1"),
        ENT_TYPE_ENUM_2("UPS", "2");

        private final String value;
        private final String code;

        COUNTRY_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (COUNTRY_TYPE_ENUM type : COUNTRY_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 国别类型
     */
    public enum CONTINENT_ENUM {
        CONTINENT_ENUM_1000("欧洲", "1000"),
        CONTINENT_ENUM_1001("非洲", "1001"),
        CONTINENT_ENUM_1002("亚洲", "1002"),
        CONTINENT_ENUM_1003("南美洲", "1003"),
        CONTINENT_ENUM_1004("北美洲", "1004"),
        CONTINENT_ENUM_1005("南极洲", "1005"),
        CONTINENT_ENUM_1006("大洋洲", "1006");

        private final String value;
        private final String code;

        CONTINENT_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (CONTINENT_ENUM type : CONTINENT_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    public enum PLAN_STATUS_ENUM {
        PLAN_STATUS_ENUM_0("暂存", "0"),
        PLAN_STATUS_ENUM_1("待装箱", "1"),
        PLAN_STATUS_ENUM_2("装箱完成", "2"),
        PLAN_STATUS_ENUM_3("报关已申请", "3"),
        PLAN_STATUS_ENUM_4("已接单", "4"),
        PLAN_STATUS_ENUM_5("申请驳回", "5");

        private final String value;
        private final String code;

        PLAN_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (PLAN_STATUS_ENUM type : PLAN_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (PLAN_STATUS_ENUM type : PLAN_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    public enum EXIT_SPEED_ENUM {
        EXIT_SPEED_ENUM_0("加急", "0"),
        EXIT_SPEED_ENUM_1("普通", "1");

        private final String value;
        private final String code;

        EXIT_SPEED_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (EXIT_SPEED_ENUM type : EXIT_SPEED_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (EXIT_SPEED_ENUM type : EXIT_SPEED_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 单损耗信息状态
     */
    public enum APPR_STATUS_MAP {
        APPR_STATUS_MAP_1("内审退回", "-1"),
        APPR_STATUS_MAP_0("暂存", "0"),
        APPR_STATUS_MAP_2("待审核", "2"),
        APPR_STATUS_MAP_8("内审通过", "8"),
        APPR_STATUS_MAP_J0("发送备案中", "J0"),
        APPR_STATUS_MAP_J1("发送备案失败", "J1"),
        APPR_STATUS_MAP_JA("发送备案", "JA"),
        APPR_STATUS_MAP_JC("备案通过", "JC"),
        APPR_STATUS_MAP_JD("备案退回", "JD");

        private final String value;
        private final String code;

        APPR_STATUS_MAP(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (APPR_STATUS_MAP type : APPR_STATUS_MAP.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (APPR_STATUS_MAP type : APPR_STATUS_MAP.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 日月新工厂代码枚举
     */
    public enum ATX_FACTORY_ENUM {
        ATX_SZ("ATXSZ", "3205240255"),
        ATX_KS("ATXKS", "3223942239");

        private final String value;
        private final String code;

        ATX_FACTORY_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (ATX_FACTORY_ENUM type : ATX_FACTORY_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (ATX_FACTORY_ENUM type : ATX_FACTORY_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * OCR识别附件类型枚举
     */
    public enum OCR_FILE_TYPE_ENUM {
        INVOICE("invoiceNo", "INVOICE"),
        PACKING("boxNo", "PACKING"),
        LADING("emsListNo", "LADING");

        private final String value;
        private final String code;

        OCR_FILE_TYPE_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (OCR_FILE_TYPE_ENUM type : OCR_FILE_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (OCR_FILE_TYPE_ENUM type : OCR_FILE_TYPE_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 申报状态类型枚举(外发加工申报表)
     */
    public enum APP_STATUS_ENUM {
        APP_STATUS_A0("暂存", "A0"),
        APP_STATUS_A1("申报中", "A1"),
        APP_STATUS_0("申报中", "0"),
        APP_STATUS_Y("申报中", "Y"),
        APP_STATUS_A2("退单", "A2"),
        APP_STATUS__1("退单", "-1"),
        APP_STATUS_Z("退单", "Z"),
        APP_STATUS_2("退单", "2"),
        APP_STATUS_3("转人工", "3"),
        APP_STATUS_1("审批通过", "1");

        private final String value;
        private final String code;

        APP_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的 code, 返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (APP_STATUS_ENUM type : APP_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (APP_STATUS_ENUM type : APP_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 发送状态类型枚举(外发加工申报表)
     */
    public enum SEND_STATUS_ENUM {
        SEND_STATUS_0("未发送", "0"),
        SEND_STATUS_1("发送中", "1"),
        SEND_STATUS_2("发送成功", "2"),
        SEND_STATUS_3("发送失败", "3");

        private final String value;
        private final String code;

        SEND_STATUS_ENUM(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (SEND_STATUS_ENUM type : SEND_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (SEND_STATUS_ENUM type : SEND_STATUS_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 内审状态
     */
    public enum AUDIT_STATUS_MAP {
        APPR_STATUS_0("暂存", "0"),
        APPR_STATUS_9("草单生成", "9"),
        APPR_STATUS_2("待审核", "2"),
        APPR_STATUS_1("内审退回", "-1"),
        APPR_STATUS_8("内审通过", "8"),
        APPR_STATUS_C("清单撤回", "C"),
        APPR_STATUS_R("报关单撤回", "R");

        private final String value;
        private final String code;

        AUDIT_STATUS_MAP(String value, String code) {
            this.value = value;
            this.code = code;
        }
        public String getValue() {
            return value;
        }
        public String getCode() {
            return code;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (AUDIT_STATUS_MAP type : AUDIT_STATUS_MAP.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
        public static String getCodeValue(String code) {
            for (AUDIT_STATUS_MAP type : AUDIT_STATUS_MAP.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 报关单类型 0:有纸报关,D:无纸带清单报关,L:有纸带清单报关,M:通关无纸化,W:无纸报关
     */
    public enum TRAF_MODE_CUSTOM_ENUM {
        HY("海运","A"),
        GL("公路","B"),
        TL("铁路","C"),
        KY("空运","D"),
        KD("快递","E");

        private String value;

        private String code;

        TRAF_MODE_CUSTOM_ENUM(String value, String code) {
            this.value = value; this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (TRAF_MODE_CUSTOM_ENUM type : TRAF_MODE_CUSTOM_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 自定义柜型枚举
     */
    public enum CONTAINER_TYPE_CUSTOM_ENUM {
        GP20("20GP","A"),
        GP40("40GP","B"),
        HQ40("40HQ","C"),
        GP45("45GP","D"),
        HQ45("45HQ","H"),
        SH("散货","E"),
        KY("空运","F"),
        KD("快递","G");

        private String value;

        private String code;

        CONTAINER_TYPE_CUSTOM_ENUM(String value, String code) {
            this.value = value; this.code = code;
        }

        public String getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的code，返回类型的value
         *
         * @param code 类型code
         */
        public static String getValue(String code) {
            for (CONTAINER_TYPE_CUSTOM_ENUM type : CONTAINER_TYPE_CUSTOM_ENUM.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 附件类型
     */
    public enum ATTACHE_STATUS_TYPE {
        STATUS_INVOICENO("发票箱单中的发票", "invoiceNo"),
        STATUS_BOXNO("发票箱单中的箱单", "boxNo"),
        STATUS_PACTCODE("合同 ", "pactCode"),
        STATUS_EMSLISTNO("提运单", "emsListNo"),
        STATUS_ORIGIN("原产地证", "origin"),
        STATUS_SITUATION("情况说明", "situation"),
        STATUS_TAX("税单", "tax"),
        STATUS_ENTRYNO("报关单", "entryNo"),
        STATUS_INVOICEBOX("发票&箱单", "invoiceBox"),
        STATUS_INVBOXBILL("发票&箱单&提单", "invBoxBill"),
        STATUS_REVIEW("复核单", "review"),
        STATUS_OTHER("其他", "other"),
        STATUS_ORDERNO("订单", "orderNo"),
        STATUS_CLASSIFICATION("归类证书", "Classification"),
        STATUS_ARRIVAL("到货通知", "arrival"),
        STATUS_PICTURE("图片", "picture"),
        STATUS_DECLARATION("申报资料", "Declaration"),
        STATUS_INSPECTION("商检证书", "inspection"),
        STATUS_DRAWING("图纸", " drawing"),
        STATUS_FREIGHTLIST("运费清单", "freightlist"),
        STATUS_VOUCHER("出入库凭证", "voucher"),
        STATUS_ATTORNEY("出口委托书", "attorney");

        private final String value;
        private final String code;

        ATTACHE_STATUS_TYPE(String value, String code) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (ATTACHE_STATUS_TYPE type : ATTACHE_STATUS_TYPE.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (ATTACHE_STATUS_TYPE type : ATTACHE_STATUS_TYPE.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }
    /**
     * 附件类型
     */
    public enum LICENSE_DOC_NEW {
        LICENSE_STATUS_01( "00000001", "发票" ),
        LICENSE_STATUS_02( "00000002", "装箱单" ),
        LICENSE_STATUS_03( "00000003", "提运单" ),
        LICENSE_STATUS_04( "00000004", "合同" ),
        LICENSE_STATUS_08( "00000008", "代理报关委托协议(纸质)" ),
        LICENSE_STATUS_09( "00000009", "原产地证据文件" ),
        LICENSE_STATUS_10( "00000010", "载货清单(舱单)" ),
        LICENSE_STATUS_11( "00000011", "特许权使用费涉及的原进口货物报关单海关编号" ),
        LICENSE_STATUS_12( "00000012", "特许权使用费合同/协议" ),
        LICENSE_STATUS_13( "00000013", "特许权使用费发票" ),
        LICENSE_STATUS_14( "00000014", "特许权使用费支付凭证" ),
        LICENSE_STATUS_15( "00000015", "代扣代缴税款纳税凭证" ),
        LICENSE_STATUS_16( "00000016", "特许权使用费其他材料" ),
        LICENSE_STATUS_101( "10000001", "代理报关委托协议(电子)" ),
        LICENSE_STATUS_102( "10000002", "减免税货物税款担保证明" ),
        LICENSE_STATUS_103( "10000003", "减免税货物税款担保延期证" ),
        LICENSE_STATUS_104( "10000004", "跨境B2B出口单证" ),
        LICENSE_STATUS_211( "20000011", "鲁医(卫生)证书" ),
        LICENSE_STATUS_212( "20000012", "动物检疫证书" ),
        LICENSE_STATUS_213( "20000013", "植物检疫证书" ),
        LICENSE_STATUS_214( "20000014", "装运前检验证书" ),
        LICENSE_STATUS_215( "20000015", "重量证书" ),
        LICENSE_STATUS_216( "20000016", "TCK检验证书(美国小麦)" ),
        LICENSE_STATUS_217( "20000017", "薰蒸证书" ),
        LICENSE_STATUS_218( "20000018", "放射性物质检测合格证明" ),
        LICENSE_STATUS_219( "20000019", "木材发货检验码单" ),
        LICENSE_STATUS_220( "20000020", "水果预检验证书" ),
        LICENSE_STATUS_221( "20000021", "中转进境确认证明文件经港澳地区中转入境水果" ),
        LICENSE_STATUS_222( "20000022", "检测报告" ),
        LICENSE_STATUS_223( "20000023", "危险特性分类鉴别报告" ),
        LICENSE_STATUS_224( "20000024", "型式试验报告" ),
        LICENSE_STATUS_225( "20000025", "动物检疫合格证明(国产原料);进境货物检疫证明、原产国检验证书(进口原料)" ),
        LICENSE_STATUS_226( "20000026", "微生物检测报告(沙门氏菌、产志贺毒素大肠杆菌、金黄色葡萄球菌、单增李斯特菌" ),
        LICENSE_STATUS_227( "20000027", "出口水产品成品检验报告" ),
        LICENSE_STATUS_228( "20000028", "检疫处理结果报告单" ),
        LICENSE_STATUS_229( "20000029", "检验报告" ),
        LICENSE_STATUS_230( "20000030", "质量安全符合性声明" ),
        LICENSE_STATUS_501( "50000001", "企业提供的证明材材料" ),
        LICENSE_STATUS_502( "50000002", "企提供的声明" ),
        LICENSE_STATUS_503( "50000003", "企业提供的标签标识" ),
        LICENSE_STATUS_504( "50000004", "企业提供的其他" ),
        LICENSE_STATUS_505( "50000005", "农业转基因生物安全证书" ),
        LICENSE_STATUS_506( "50000006", "引进种子、苗木检疫审批单" ),
        LICENSE_STATUS_507( "50000007", "远洋自捕水产品的确认通知(文件)和远洋渔业项目确认表、农业部远洋渔业企业资格证书" ),
        LICENSE_STATUS_508( "50000008", "特种设备制造许可证" ),
        LICENSE_STATUS_509( "50000009", "进口化妆品卫生许可批件" ),
        LICENSE_STATUS_510( "50000010", "特殊医学用途配方食品注册证书" ),
        LICENSE_STATUS_151( "50000011", "保健食品注册证书/备案凭证" ),
        LICENSE_STATUS_512( "50000012", "捕捞船舶登记证和捕捞许可证(野生捕捞水生动物)" ),
        LICENSE_STATUS_513( "50000013", "化妆品生产许可证(仅限首次出口时提供)" ),
        LICENSE_STATUS_514( "50000014", "特殊用途销售包装化妆品成品应当提供相应的卫生许可批件或者具有相关资质的机构出具的是否存在安全性风险物质的有关安全性评估资料(仅限首次出口时提供)" ),
        LICENSE_STATUS_601( "60000001", "民用爆炸品进口审批单" ),
        LICENSE_STATUS_602( "60000002", "民用爆炸品出口审批单" ),
        LICENSE_STATUS_603( "60000003", "军品出口许可证" ),
        LICENSE_STATUS_604( "60000004", "人类选传资源材料出口、出境证明" ),
        LICENSE_STATUS_605( "60000005", "古生物化石出口、出境批件" ),
        LICENSE_STATUS_606( "60000006", "密码出口许可证" ),
        LICENSE_STATUS_607( "60000007", "援外项目任务通知单" ),
        LICENSE_STATUS_608( "60000008", "医疗用毒性药品进出口批件" ),
        LICENSE_STATUS_609( "60000009", "放射性药品进出口批件" ),
        LICENSE_STATUS_610( "60000010", "血液出口批件" ),
        LICENSE_STATUS_611( "60000011", "化学品进出口环境管理登记证明" );


        private final String code;
        private final String value;

        LICENSE_DOC_NEW(String code, String value) {
            this.value = value;
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据类型的 code，返回类型的 value
         *
         * @param code 类型 code
         */
        public static String getValue(String code) {
            for (LICENSE_DOC_NEW type : LICENSE_DOC_NEW.values()) {
                if (type.getCode().equals(code)) {
                    return type.getValue();
                }
            }
            return "";
        }

        public static String getCodeValue(String code) {
            for (LICENSE_DOC_NEW type : LICENSE_DOC_NEW.values()) {
                if (type.getCode().equals(code)) {
                    return code + " " + type.getValue();
                }
            }
            return "";
        }
    }


    /**
     * LOCAL模式获取数据业务类型
     */
    public enum LOCAL_DATA_TYPE_ENUM {
        SUB_ENTRY("SUB_ENTRY_DATA", "订阅报关单");

        private String type;
        private String name;

        public String getType() {
            return type;
        }

        public String getName() {
            return name;
        }

        LOCAL_DATA_TYPE_ENUM(String type,String name) {
            this.type = type;
            this.name = name;
        }

        /**
         * 根据类型的 type，返回 name
         *
         */
        public static String getValue(String type) {
            for (LOCAL_DATA_TYPE_ENUM local : LOCAL_DATA_TYPE_ENUM.values()) {
                if (local.getType().equals(type)) {
                    return local.getName();
                }
            }
            return "";
        }
    }


    /**
     * 通用业务类型
     */
    public enum COMMON_BUSINESS_TYPE_ENUM {
        STATE_TRADE_IMPORT_CIGARETTE("1", "国营贸易进口卷烟"),
        STATE_TRADE_IMPORT_ACCESSORIES("2", "国营贸易进口辅料"),
        STATE_TRADE_IMPORT_EQUIPMENT("3", "国营贸易进口卷烟设备"),
        STATE_TRADE_IMPORT_FILAMENT("4", "国营贸易进口丝束"),
        STATE_TRADE_DOMESTIC_FILAMENT("5", "国营贸易内购内销丝束"),
        NON_STATE_TRADE_IMPORT_ACCESSORIES("6", "非国营贸易进口辅料"),
        PROCESSING_IMPORT_SHEET("7", "出料加工进口薄片"),
        EXPORT_EQUIPMENT("8", "出口烟机设备"),
        EXPORT_ACCESSORIES("9", "出口辅料");

        private String type;
        private String name;

        COMMON_BUSINESS_TYPE_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (COMMON_BUSINESS_TYPE_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }
    public enum COMMON_BUSINESS2_TYPE_ENUM {
        STATE_TRADE_IMPORT_CIGARETTE("1", "国营贸易进口卷烟"),
        STATE_TRADE_IMPORT_ACCESSORIES("2", "国营贸易进口辅料"),
        STATE_TRADE_IMPORT_EQUIPMENT("3", "国营贸易进口烟机设备"),
        STATE_TRADE_IMPORT_FILAMENT("4", "国营贸易进口丝束"),
        STATE_TRADE_DOMESTIC_FILAMENT("5", "国营贸易内购内销丝束"),
        NON_STATE_TRADE_IMPORT_ACCESSORIES("6", "非国营贸易进口辅料"),
        PROCESSING_IMPORT_SHEET("7", "出境加工进口薄片"),
        EXPORT_EQUIPMENT("8", "出口烟机设备"),
        EXPORT_ACCESSORIES("9", "出口辅料");

        private String type;
        private String name;

        COMMON_BUSINESS2_TYPE_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (COMMON_BUSINESS2_TYPE_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    /**
     * 授权状态
     */
    public enum IS_AUTHORIZE_LIST_ENUM {
        UNAUTHORIZED("0", "未授权"),
        AUTHORIZED("1", "已授权");

        private String type;
        private String name;

        IS_AUTHORIZE_LIST_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (IS_AUTHORIZE_LIST_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    /**
     * 启用状态
     */
    public enum STATUS_ENUM {
        ENABLED("0", "启用"),
        DISABLED("1", "停用");


        private String type;
        private String name;

        STATUS_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (STATUS_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    /**
     * 数据状态
     */
    public enum DATA_STATUS_ENUM {
        ENABLED("0", "启用"),
        INVALID("1", "作废");

        private String type;
        private String name;

        DATA_STATUS_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (DATA_STATUS_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    /**
     * 命名方式类型
     */
    public enum NAME_METHOD_TYPE_ENUM {
        BRAND_CONSISTENT("0", "与牌号保持一致"),
        TRADEMARK_CONSISTENT("1", "与商标名称保持一致");
        private String type;
        private String name;

        NAME_METHOD_TYPE_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (NAME_METHOD_TYPE_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }



    /**
     * 单据状态
     */
    public enum STATE_ENUM {
        DRAFT("0", "编制"),
        CONFIRMED("1", "确认"),
        INVALID("2", "作废");

        private String type;
        private String name;

        STATE_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (STATE_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    /**
     * 是否判断
     */
    public enum IS_NOT_ENUM {
        YES("0", "是"),
        NO("1", "否");

        private String type;
        private String name;

        IS_NOT_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (IS_NOT_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }


    /**
     * 示例完整实现（以CREDIT_LEVEL_ENUM为例）：
     */
    public enum CREDIT_LEVEL_ENUM {
        HIGH_CERTIFICATION("1", "高级认证企业"),
        REGISTERED("2", "注册登记和备案企业"),
        DISHONEST("4", "失信企业");

        private String type;
        private String name;

        CREDIT_LEVEL_ENUM(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() { return type; }
        public String getName() { return name; }

        public static String getValue(String type) {
            for (CREDIT_LEVEL_ENUM e : values()) {
                if (e.getType().equals(type)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    public enum RED_FLUSH_ENUM {
        YES("是", "0"),
        NO("否", "1");

        private final String label;
        private final String value;

        RED_FLUSH_ENUM(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据类型的 value，返回类型的 label
         *
         * @param value 类型 value
         */
        public static String getValue(String value) {
            for (RED_FLUSH_ENUM status : RED_FLUSH_ENUM.values()) {
                if (status.getValue().equals(value)) {
                    return status.getLabel();
                }
            }
            return "";
        }
    }

    @RequiredArgsConstructor
    @Getter
    public enum TRANSPORT_MODE {
        SEA("0", "海运"),
        AIR("1", "空运"),
        LAND("2", "陆运");

        private final String value;
        private final String label;

        public static String getValue(String value) {
            for (TRANSPORT_MODE transportMode : TRANSPORT_MODE.values()) {
                if (transportMode.getValue().equals(value)) {
                    return transportMode.getLabel();
                }
            }
            return StringUtils.EMPTY;
        }
    }
}
