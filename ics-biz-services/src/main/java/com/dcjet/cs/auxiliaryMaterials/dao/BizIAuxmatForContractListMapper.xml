<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractListMapper">
    <resultMap id="bizIAuxmatForContractListResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="product_name" property="productName" jdbcType="VARCHAR" />
		<result column="import_quantity" property="importQuantity" jdbcType="NUMERIC" />
		<result column="import_unit" property="importUnit" jdbcType="VARCHAR" />
		<result column="quantity" property="quantity" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="unit_price" property="unitPrice" jdbcType="NUMERIC" />
		<result column="amount" property="amount" jdbcType="NUMERIC" />
		<result column="delivery_date" property="deliveryDate" jdbcType="TIMESTAMP" />
		<result column="usd_total" property="usdTotal" jdbcType="NUMERIC" />
		<result column="specification" property="specification" jdbcType="VARCHAR" />
		<result column="product_category" property="productCategory" jdbcType="VARCHAR" />
		<result column="data_state" property="dataState" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="order_no" property="orderNo" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,head_id
     ,product_name
     ,import_quantity
     ,import_unit
     ,quantity
     ,unit
     ,unit_price
     ,amount
     ,delivery_date
     ,usd_total
     ,specification
     ,product_category
     ,data_state
     ,version_no
     ,trade_code
     ,sys_org_code
     ,create_by
     ,create_time
     ,update_by
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,order_no
    </sql>
	<sql id="condition">
	<if test="headId != null and headId != ''">
		  head_id = #{headId}
	</if>
    <if test="productName != null and productName != ''">
	  and product_name like '%'|| #{productName} || '%'
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIAuxmatForContractListResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList">
        select <include refid="Base_Column_List" />
        from t_biz_i_auxmat_for_contract_list t
        <where>
            <!-- 用户Grid查询 and 条件-->
			<include refid="condition"></include>
        </where>
        order by t.create_time desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_auxmat_for_contract_list t
		where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
              #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from t_biz_i_auxmat_for_contract_list t
		where
		t.head_id
		in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from t_biz_i_auxmat_for_contract_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <select id="getContractTotal"
            resultType="com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListDto">
        select
            sum(import_quantity) as import_quantity,
            sum(quantity) as quantity,
            sum(amount) as amount,
            sum(usd_total) as usd_total
        from t_biz_i_auxmat_for_contract_list t
        <where>
            <!-- 用户Grid查询 and 条件-->
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getPlanListPaged" resultType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList">
        SELECT
            a.ORDER_NO,
            a.ORDER_DATE,
            a.SUPPLIER,
--             a.qty AS notice_qty,
--             COALESCE(b.import_qty, 0) AS import_qty,
            a.qty - COALESCE(b.import_qty, 0) AS diff_qty
        FROM (
                 SELECT
                     nh.ORDER_NO,
                     nh.ORDER_DATE,
                     nl.SUPPLIER,
                     SUM(nl.QTY) AS qty
                 FROM T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD nh
                          LEFT JOIN T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST nl ON nh.id = nl.HEAD_ID
                 WHERE nh.DATA_STATUS = '1' and nh.trade_code = #{tradeCode}
                 GROUP BY nh.ORDER_NO, nh.ORDER_DATE, nl.SUPPLIER
             ) a
                 LEFT JOIN (
            SELECT
                cl.ORDER_NO,
                cl.SUPPLIER,
                SUM(cl.IMPORT_QUANTITY) AS import_qty
            FROM T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD ch
                     LEFT JOIN T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST cl ON ch.id = cl.HEAD_ID
            WHERE ch.DOC_STATUS != '2' and ch.trade_code = #{tradeCode}
            GROUP BY cl.ORDER_NO, cl.SUPPLIER
        ) b ON a.ORDER_NO = b.ORDER_NO AND a.SUPPLIER = b.SUPPLIER
        WHERE a.qty - COALESCE(b.import_qty, 0) > 0
        <if test="orderNo != null and orderNo != ''">
            and a.ORDER_NO like '%'|| #{orderNo} || '%'
        </if>
    </select>
    <select id="getBizIAuxmatForContractListByHeadid"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList">
        select
            <include refid="Base_Column_List"></include>
        from t_biz_i_auxmat_for_contract_list t
        where t.head_id = #{headId}
    </select>
    <insert id="insertByOrderNoList">
        insert into t_biz_i_auxmat_for_contract_list(
                                                      id
                                                    ,head_id
                                                    ,product_name
                                                    ,import_quantity
                                                    ,import_unit
                                                    ,unit
                                                    ,unit_price
                                                    ,amount
                                                    ,usd_total
                                                    ,specification
                                                    ,product_category
                                                    ,data_state
                                                    ,parent_id
                                                    ,version_no
                                                    ,trade_code
                                                    ,create_by
                                                    ,create_time
                                                    ,insert_user_name
                                                    ,order_no
                                                    ,supplier
        )
        select
            sys_guid(),
            #{sid},
            nl.PRODUCT_NAME,
            nl.QTY,
            nl.UNIT,
            nl.UNIT,
            q.import_unit_price,
            COALESCE(nl.QTY , 0) * COALESCE(q.import_unit_price, 0),
            COALESCE(nl.QTY , 0) * COALESCE(q.import_unit_price, 0),
            nl.SPECIFICATION,
            q.MERCHANDISE_CATEGORIES,
            '0',
            nl.id,
            '1',
            nh.TRADE_CODE,
            #{userNo},
            now(),
            #{userName},
            nh.ORDER_NO,
            nl.SUPPLIER
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST nl
                 left join T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD nh on nh.ID = nl.HEAD_ID
                 left join t_biz_quotation q on q.G_NAME = nl.PRODUCT_NAME and q.specifications = nl.SPECIFICATION
        where concat_ws('_',nh.ORDER_NO, nl.SUPPLIER) in
        <foreach collection="orderNoList"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </insert>
    <select id="getContractListByHeadId" resultType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList">
        select * from t_biz_i_auxmat_for_contract_list
        where head_id = #{headId}
        order by create_time
    </select>
    <update id="updateCorrelationID">
        update T_BIZ_INCOMING_GOODS_LIST set CONTRACT_LIST_ID = #{newListId} where CONTRACT_LIST_ID = #{oldSid};
        update T_BIZ_INCOMING_GOODS_HEAD set PARENT_ID = #{headId} where PARENT_ID = #{oldHeadId};
    </update>
    <select id="checkNextModuleExistEffectiveData" resultType="java.lang.Integer">
        select count(1) from T_BIZ_INCOMING_GOODS_LIST l
                left join T_BIZ_INCOMING_GOODS_HEAD h on h.id = l.HEAD_ID
        where
            l.CONTRACT_LIST_ID = #{id}
          and h.DATA_STATE != '2';
    </select>
    <insert id="insertByHeadId">
        insert into t_biz_i_auxmat_for_contract_list(
                                                      id
                                                    ,head_id
                                                    ,product_name
                                                    ,import_unit
                                                    ,import_quantity
                                                    ,quantity
                                                    ,unit
                                                    ,unit_price
                                                    ,specification
                                                    ,product_category
                                                    ,data_state
                                                    ,version_no
                                                    ,trade_code
                                                    ,create_by
                                                    ,create_time
                                                    ,insert_user_name
        )
        select
            sys_guid(),
            #{headId},
            G_NAME,
            UNIT_I,
            0,
            0,
            UNIT,
            IMPORT_UNIT_PRICE,
            SPECIFICATIONS,
            MERCHANDISE_CATEGORIES,
            '0',
            '1',
            TRADE_CODE,
            #{userNo},
            now(),
            #{userName}
        from T_BIZ_QUOTATION
        where
            sid in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </insert>
    <select id="getPurchaseSalesContractNo" resultType="java.lang.String">
        select
            LISTAGG(distinct nh.PURCHASE_SALES_CONTRACT_NO, ',')
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST nl
        left join T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD nh on nh.ID = nl.HEAD_ID
        left join t_biz_quotation q on q.G_NAME = nl.PRODUCT_NAME and q.specifications = nl.SPECIFICATION
        where concat_ws('_',nh.ORDER_NO, nl.SUPPLIER) in
        <foreach collection="orderNoList"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

</mapper>
