<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractMapper">
    <resultMap id="bizIAuxmatBuyContractResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract">
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
        <result column="contract_year" property="contractYear" jdbcType="VARCHAR" />
        <result column="business_distinction" property="businessDistinction" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="version_no" property="versionNo" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
        <result column="appr_status" property="apprStatus" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_by_name" property="createByName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="update_by_name" property="updateByName" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
        <result column="CONTRACT_AMOUNT" property="contractAmount" jdbcType="DECIMAL" />
        <result column="EXCHANGE_RATE" property="exchangeRate" jdbcType="DECIMAL" />
        <result column="TARIFF_RATE" property="tariffRate" jdbcType="DECIMAL" />
        <result column="TARIFF_AMOUNT" property="tariffAmount" jdbcType="DECIMAL" />
        <result column="CONSUMPTION_TAX_RATE" property="consumptionTaxRate" jdbcType="DECIMAL" />
        <result column="CONSUMPTION_TAX_AMOUNT" property="consumptionTaxAmount" jdbcType="DECIMAL" />
        <result column="VAT_RATE" property="vatRate" jdbcType="DECIMAL" />
        <result column="VAT_AMOUNT" property="vatAmount" jdbcType="DECIMAL" />
        <result column="IMPORT_EXPORT_AGENCY_RATE" property="importExportAgencyRate" jdbcType="DECIMAL" />
        <result column="IMPORT_EXPORT_AGENCY_FEE" property="importExportAgencyFee" jdbcType="DECIMAL" />
        <result column="HEADQUARTERS_AGENCY_RATE" property="headquartersAgencyRate" jdbcType="DECIMAL" />
        <result column="HEADQUARTERS_AGENCY_FEE" property="headquartersAgencyFee" jdbcType="DECIMAL" />
        <result column="CONTRACT_QUANTITY" property="contractQuantity" jdbcType="DECIMAL" />
        <result column="BILLING_WEIGHT" property="billingWeight" jdbcType="DECIMAL" />
        <result column="CUSTOMS_CLEARANCE_FEE" property="customsClearanceFee" jdbcType="DECIMAL" />
        <result column="CONTAINER_INSPECTION_FEE" property="containerInspectionFee" jdbcType="DECIMAL" />
        <result column="FREIGHT_FORWARDER_FEE" property="freightForwarderFee" jdbcType="DECIMAL" />
        <result column="INSURANCE_RATE" property="insuranceRate" jdbcType="DECIMAL" />
        <result column="INSURANCE_FEE" property="insuranceFee" jdbcType="DECIMAL" />
        <result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="DECIMAL" />
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
        <result column="createrUser" property="createrUser" jdbcType="VARCHAR" />
        <result column="createrTime" property="createrTime" jdbcType="TIMESTAMP" />
        <result column="is_transfer_notice" property="isTransferNotice" jdbcType="VARCHAR" />
        <result column="g_name" property="gName" jdbcType="VARCHAR" />
        <result column="supplier" property="supplier" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,business_type
        ,contract_no
        ,contract_year
        ,business_distinction
        ,remark
        ,version_no
        ,status
        ,confirm_time
        ,appr_status
        ,create_by
        ,create_by_name
        ,create_time
        ,update_by
        ,update_by_name
        ,update_time
        ,sys_org_code
        ,trade_code
        ,CONTRACT_AMOUNT
        ,EXCHANGE_RATE
        ,TARIFF_RATE
        ,TARIFF_AMOUNT
        ,CONSUMPTION_TAX_RATE
        ,CONSUMPTION_TAX_AMOUNT
        ,VAT_RATE
        ,VAT_AMOUNT
        ,IMPORT_EXPORT_AGENCY_RATE
        ,IMPORT_EXPORT_AGENCY_FEE
        ,HEADQUARTERS_AGENCY_RATE
        ,HEADQUARTERS_AGENCY_FEE
        ,CONTRACT_QUANTITY
        ,BILLING_WEIGHT
        ,CUSTOMS_CLEARANCE_FEE
        ,CONTAINER_INSPECTION_FEE
        ,FREIGHT_FORWARDER_FEE
        ,INSURANCE_RATE
        ,INSURANCE_FEE
        ,PAYMENT_AMOUNT
        ,EXTEND1
        ,EXTEND2
        ,EXTEND3
        ,EXTEND4
        ,EXTEND5
        ,EXTEND6
        ,EXTEND7
        ,EXTEND8
        ,EXTEND9
        ,EXTEND10
        ,COALESCE(t.update_by_name,t.create_by_name) as createrUser
        ,COALESCE(t.UPDATE_TIME,t.create_time) as createrTime
        ,is_transfer_notice
    </sql>
    <sql id="condition">
        <if test="contractNo != null and contractNo != ''">
            and contract_no like '%'|| #{contractNo} || '%'
        </if>
        <if test="contractYear != null and contractYear != ''">
            and contract_year = #{contractYear}
        </if>
        <if test="status == null or status == ''">
            and STATUS !='2'
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and trade_code = #{tradeCode}
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) <= DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    </sql>
    <update id="updateCancelByContract">
        UPDATE t_biz_i_auxmat_buy_contract h
        SET status = '2'
        where contract_no = #{contractNo}
          and trade_code = #{tradeCode}
    </update>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIAuxmatBuyContractResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract">
        SELECT
        <include refid="Base_Column_List" />
        ,case  when (select count(*) from t_biz_i_auxmat_buy_contract head
        left join T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD ctr on ctr.PURCHASE_SALES_CONTRACT_NO=head.contract_no
        where head.id=t.id and ctr.DATA_STATUS!='2') > 0 Then '1' ELSE '0' END as hasHeadNotice,
        (SELECT CASE WHEN head.version_no != 1 THEN 1 ELSE 0 END
        FROM t_biz_i_auxmat_buy_contract head
        WHERE head.id = t.id ) as isCopy
        FROM
        t_biz_i_auxmat_buy_contract t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.UPDATE_TIME,t.create_time) desc
    </select>
    <select id="checkContractId" resultType="java.lang.Integer">
        select count(1) from t_biz_i_auxmat_buy_contract t
        where t.contract_no = #{contractNo} and TRADE_CODE = #{tradeCode}
                and status != '2'
        <if test="id != null and id != ''">
            AND t.ID != #{id}
        </if>
    </select>
    <select id="checkOrderStatus" resultType="java.lang.Integer">
        select count(*)
        from t_biz_i_auxmat_buy_contract
        where status != #{status}
        and id in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="checkOrderUsed" resultType="java.lang.Integer">
        select count(*)
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        where PURCHASE_SALES_CONTRACT_NO in (
        select distinct contract_no
        from t_biz_i_auxmat_buy_contract
        where id in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>
    <select id="checkContractIdNotCancel" resultType="java.lang.String">
        select
            id
        from t_biz_i_auxmat_buy_contract t
        where t.contract_no in  (select  distinct contract_no
                             from t_biz_i_auxmat_buy_contract t
                             where ID = #{id}
        ) and  t.STATUS !=2;
    </select>
    <select id="getMaxVersionNoByContractNo"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract">
        select id,VERSION_NO  from t_biz_i_auxmat_buy_contract
        where contract_no = #{contractNo}
          and trade_code = #{tradeCode}
        order by CAST(VERSION_NO AS INTEGER) desc limit 1
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_auxmat_buy_contract t where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="calculateContractAmounts" resultMap="bizIAuxmatBuyContractResultMap">
        WITH contract_amounts AS (
            SELECT
                a.id,
                a.business_type,
                a.trade_code,
                COALESCE((SELECT SUM(amount) FROM t_biz_i_auxmat_buy_contract_list WHERE head_id = a.id), 0) as CONTRACT_AMOUNT,
                COALESCE(<![CDATA[#{exchangeRate}]]>,(SELECT FLOAT_RATE FROM t_biz_i_auxmat_buy_contract_list detail
                 left join T_BIZ_RATE_TABLE rate on rate.curr=detail.curr and detail.trade_code=rate.trade_code WHERE head_id = a.id LIMIT 1)) as EXCHANGE_RATE,
                COALESCE(tr.TARIFF_RATE, 0) / 100 as TARIFF_RATE,
                COALESCE(tr.CONSUMPTION_TAX_RATE, 0) / 100 as CONSUMPTION_TAX_RATE,
                COALESCE(tr.VAT_RATE, 0) / 100 as VAT_RATE,
                COALESCE(tr.IE_AGENT_FEE_RATE, 0) / 100 as IMPORT_EXPORT_AGENCY_RATE,
                COALESCE(tr.HQ_AGENT_FEE_RATE, 0) / 100 as HEADQUARTERS_AGENCY_RATE,
                COALESCE(tr.INTL_FREIGHT_AMT, 0) as INTL_FREIGHT_AMT,
                COALESCE(tr.PORT_CHARGES_AMT, 0) as PORT_CHARGES_AMT,
                COALESCE(tr.LAND_FREIGHT_AMT, 0) as LAND_FREIGHT_AMT,
                COALESCE(tr.CUSTOMS_FEE_AMT, 0) as CUSTOMS_CLEARANCE_FEE,
                COALESCE(tr.CNTR_INSP_FEE_AMT, 0) as CONTAINER_INSPECTION_FEE,
                COALESCE(tr.INSURANCE_RATE, 0) as INSURANCE_RATE,
                COALESCE(tr.CONTAINER_CAP, '20') as CONTAINER_CAP,
                COALESCE((SELECT SUM(qty) FROM t_biz_i_auxmat_buy_contract_list WHERE head_id = a.id), 0) as CONTRACT_QUANTITY,
                COALESCE((SELECT curr FROM t_biz_i_auxmat_buy_contract_list WHERE head_id = a.id LIMIT 1), 'USD') as currency,
                (select g_name from t_biz_i_auxmat_buy_contract_list WHERE head_id = a.id LIMIT 1) as g_name,
                (select supplier from t_biz_i_auxmat_buy_contract_list WHERE head_id = a.id LIMIT 1) as supplier
            FROM t_biz_i_auxmat_buy_contract a
            left join T_BIZ_TRANSCODE tr on a.business_type = tr.biz_type AND tr.trade_code = a.trade_code
            WHERE a.id = #{headId}
        ),
        base_calculations AS (
            SELECT 
                ca.*,
                COALESCE(ca.CONTRACT_AMOUNT * ca.EXCHANGE_RATE, 0) as base_amount,
                CASE 
                    WHEN EXISTS (SELECT 1 FROM t_biz_i_auxmat_buy_contract_list WHERE head_id = ca.id AND unit = '千克')
                    THEN CEIL(COALESCE(ca.CONTRACT_QUANTITY, 0) / 1000 /
                        CASE WHEN ca.CONTAINER_CAP = '10' THEN 10 ELSE 20 END)
                    ELSE CEIL(COALESCE(ca.CONTRACT_QUANTITY, 0))
                END as BILLING_WEIGHT
            FROM contract_amounts ca
        ),
        tax_calculations AS (
            SELECT 
                bc.*,

                ROUND(COALESCE(bc.base_amount * bc.TARIFF_RATE, 0), 2) as TARIFF_AMOUNT,

                ROUND(COALESCE((bc.base_amount + ROUND(COALESCE(bc.base_amount * bc.TARIFF_RATE, 0), 2)) * bc.CONSUMPTION_TAX_RATE, 0), 6) as CONSUMPTION_TAX_AMOUNT,

                ROUND(COALESCE((bc.base_amount + 
                      ROUND(COALESCE(bc.base_amount * bc.TARIFF_RATE, 0), 2) + 
                      ROUND(COALESCE((bc.base_amount + ROUND(COALESCE(bc.base_amount * bc.TARIFF_RATE, 0), 2)) * bc.CONSUMPTION_TAX_RATE, 0), 6)) * bc.VAT_RATE, 0), 6) as VAT_AMOUNT,

                ROUND(COALESCE(bc.base_amount * bc.IMPORT_EXPORT_AGENCY_RATE, 0), 2) as IMPORT_EXPORT_AGENCY_FEE,

                ROUND(COALESCE(bc.base_amount * bc.HEADQUARTERS_AGENCY_RATE, 0), 2) as HEADQUARTERS_AGENCY_FEE,

                ROUND(COALESCE((bc.INTL_FREIGHT_AMT + bc.PORT_CHARGES_AMT + bc.LAND_FREIGHT_AMT) * COALESCE(#{billingWeight},bc.BILLING_WEIGHT) +
                      bc.CUSTOMS_CLEARANCE_FEE + bc.CONTAINER_INSPECTION_FEE, 0), 2) as FREIGHT_FORWARDER_FEE,

                ROUND(COALESCE(bc.CONTRACT_AMOUNT * 1.1 * bc.EXCHANGE_RATE * bc.INSURANCE_RATE, 0), 2) as INSURANCE_FEE
            FROM base_calculations bc
        )
        SELECT
            a.contract_no,

            tc.*,

            CASE
                WHEN COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                   tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) >= 10000000
                THEN CEIL(COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                   tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) / 1000000) * 1000000
                WHEN COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                   tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) >= 1000000
                THEN CEIL(COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                   tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) / 100000) * 100000
                ELSE CEIL(COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                   tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) / 10000) * 10000
            END as PAYMENT_AMOUNT
        FROM t_biz_i_auxmat_buy_contract a
        JOIN tax_calculations tc ON a.id = tc.id
        WHERE a.id = #{headId}
    </select>
    <select id="saveTransferNotice">
        update t_biz_i_auxmat_buy_contract
        set
            is_transfer_notice = '1',
            CONTRACT_AMOUNT = #{contractAmount},
            EXCHANGE_RATE = #{exchangeRate},
            TARIFF_RATE = #{tariffRate},
            TARIFF_AMOUNT = #{tariffAmount},
            CONSUMPTION_TAX_RATE = #{consumptionTaxRate},
            CONSUMPTION_TAX_AMOUNT = #{consumptionTaxAmount},
            VAT_RATE = #{vatRate},
            VAT_AMOUNT = #{vatAmount},
            IMPORT_EXPORT_AGENCY_RATE = #{importExportAgencyRate},
            IMPORT_EXPORT_AGENCY_FEE = #{importExportAgencyFee},
            HEADQUARTERS_AGENCY_RATE = #{headquartersAgencyRate},
            HEADQUARTERS_AGENCY_FEE = #{headquartersAgencyFee},
            CONTRACT_QUANTITY = #{contractQuantity},
            BILLING_WEIGHT = #{billingWeight},
            CUSTOMS_CLEARANCE_FEE = #{customsClearanceFee},
            CONTAINER_INSPECTION_FEE = #{containerInspectionFee},
            FREIGHT_FORWARDER_FEE = #{freightForwarderFee},
            INSURANCE_RATE = #{insuranceRate},
            INSURANCE_FEE = #{insuranceFee},
            PAYMENT_AMOUNT = #{paymentAmount}
        where id = #{id}
    </select>
    <select id="getRequestDeliverDate" resultType="java.lang.String">
        select to_char(REQUEST_DELIVERY_DATE,'YYYY"年"MM"月"')
        from t_biz_i_auxmat_buy_contract buy
        left join T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD orderh on orderh.PURCHASE_SALES_CONTRACT_NO=buy.contract_no and orderh.data_status!=2
        left join T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST orderl on orderl.head_id=orderh.id
        where buy.id=#{sid}
        order by orderh.CREATE_TIME desc
        Limit 1

    </select>
</mapper>
