package com.dcjet.cs.auxiliaryMaterials.service;

import com.aspose.cells.PdfSaveOptions;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.attach.dao.AttachedMapper;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.OrderNoticeListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.OrderNoticeHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead;
import com.dcjet.cs.quo.dao.BizQuotationMapper;
import com.dcjet.cs.quo.model.BizQuotation;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.FileUtil;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.common.util.DateUtils;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderNoticeHeadService extends BaseService<OrderNoticeHead> {
    private final static String CONTRACT_SEPARATOR = ",", DIR = "DIR";
    private final static String INITIAL_VERSION_NO = "1", INITIAL_PORT = "CHN331",
            INITIAL_TRANSPORT_MODE = CommonEnum.TRANSPORT_MODE.SEA.getValue();
    private final static String PRINT_FIX_FINAL_USER = "上海烟草集团", PRINT_FIX_APP_COMPANY = "上海进出口",
            PRINT_FIX_IS_NEW = "否";
    private final static String PRINT_TEMPLATE_NAME = "采购订单表.xlsx";
    private final static String PRINT_FILE_NAME = "采购订单表";
    private final static String PDF_TYPE = "PDF";

    @Resource
    private OrderNoticeHeadMapper orderNoticeHeadMapper;
    @Resource
    private OrderNoticeHeadDtoMapper orderNoticeHeadDtoMapper;
    @Resource
    private OrderNoticeListMapper orderNoticeListMapper;
    @Resource
    private OrderNoticeListDtoMapper orderNoticeListDtoMapper;
    @Resource
    private BizIAuxmatBuyContractMapper bizIAuxmatBuyContractMapper;
    @Resource
    private BizIAuxmatBuyContractListMapper bizIAuxmatBuyContractListMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BizQuotationMapper bizQuotationMapper;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private ExportService exportService;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;
    @Resource
    private PCodeHolder pCodeHolder;
    @Value("${dc.export.temp:}")
    private String tempPath;


    @Override
    public Mapper<OrderNoticeHead> getMapper() {
        return this.orderNoticeHeadMapper;
    }

    /**
     * 获取分页列表
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param pageParam            分页参数
     * @param userInfo             用户信息
     * @return 订货通知表头分页列表模型
     */
    public ResultObject<List<OrderNoticeHeadDto>> getListPaged(OrderNoticeHeadParam orderNoticeHeadParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadDtoMapper.toPo(orderNoticeHeadParam);
        orderNoticeHead.setTradeCode(userInfo.getCompany());
        Page<OrderNoticeHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.orderNoticeHeadMapper.getList(orderNoticeHead));
        List<OrderNoticeHeadDto> headDtoList = page.getResult().stream()
                .map(onh -> this.orderNoticeHeadDtoMapper.toDto(onh))
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 获取所有客户
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 客户代码列表
     */
    public List<String> getAllCustomers(OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadDtoMapper.toPo(orderNoticeHeadParam);
        orderNoticeHead.setTradeCode(userInfo.getCompany());
        return this.orderNoticeHeadMapper.getAllCustomers(orderNoticeHead);
    }

    /**
     * 获取所有列表
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头列表
     */
    public List<OrderNoticeHeadDto> getAllList(OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadDtoMapper.toPo(orderNoticeHeadParam);
        orderNoticeHead.setTradeCode(userInfo.getCompany());
        List<OrderNoticeHead> orderNoticeHeadList = this.orderNoticeHeadMapper.getList(orderNoticeHead);
        if (CollectionUtils.isEmpty(orderNoticeHeadList)) {
            return Collections.emptyList();
        }
        List<OrderNoticeHeadDto> dtoList = orderNoticeHeadList.stream()
                .map(onh -> this.orderNoticeHeadDtoMapper.toDto(onh))
                .collect(Collectors.toList());
        dtoList.forEach(dto -> {
            if (StringUtils.isNotBlank(dto.getBusinessType())) {
                dto.setBusinessType(dto.getBusinessType() + StringUtils.SPACE +
                        CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(dto.getBusinessType()));
            }
            if (StringUtils.isNotBlank(dto.getCustomer())) {
                dto.setCustomer(dto.getCustomer() + StringUtils.SPACE + dto.getCustomerName());
            }
            if (StringUtils.isNotBlank(dto.getDataStatus())) {
                dto.setDataStatus(dto.getDataStatus() + StringUtils.SPACE +
                        CommonEnum.STATE_ENUM.getValue(dto.getDataStatus()));
            }
        });
        return dtoList;
    }

    /**
     * 获取购销合同选择信息
     *
     * @param userInfo 用户信息
     * @return 购销合同选择列表
     */
    public List<OrderNoticeSelectBuyContractDto> getSelectBuyContractInfo(UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        return this.orderNoticeHeadMapper.getSelectBuyContractList(tradeCode);
    }

    /**
     * 获取新增所需数据
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 新增所需数据
     */
    public OrderNoticeAddDto getSelectedAddRequiredData(OrderNoticeAddParam addParam, UserInfoToken<?> userInfo) {
        List<String> contractIds = addParam.getContractIds();
        if (CollectionUtils.isEmpty(contractIds)) {
            throw new ErrorException(500, "请选择购销合同");
        }
        List<BizIAuxmatBuyContract> buyContracts = this.getBuyContractsByIds(contractIds);
        String notConfirmedContractNos = buyContracts.stream()
                .filter(contract -> !CommonEnum.STATE_ENUM.CONFIRMED.getType().equals(contract.getStatus()))
                .map(BizIAuxmatBuyContract::getContractNo)
                .collect(Collectors.joining(CONTRACT_SEPARATOR));
        if (StringUtils.isNotBlank(notConfirmedContractNos)) {
            throw new ErrorException(500, String.format("购销合同号 %s 未确认，请重新选择", notConfirmedContractNos));
        }
        OrderNoticeAddDto requiredAddDto = new OrderNoticeAddDto();
        // 业务类型
        requiredAddDto.setBusinessType(CommonEnum.COMMON_BUSINESS_TYPE_ENUM.STATE_TRADE_IMPORT_ACCESSORIES.getType());
        // 购销合同号
        requiredAddDto.setPurchaseSalesContractNo(buyContracts.stream()
                .map(BizIAuxmatBuyContract::getContractNo)
                .collect(Collectors.joining(CONTRACT_SEPARATOR)));
        // 版本号
        requiredAddDto.setVersionNo(INITIAL_VERSION_NO);
        // 制单人
        requiredAddDto.setCreateBy(userInfo.getUserNo());
        // 制单人姓名
        requiredAddDto.setCreateByName(userInfo.getUserName());
        // 制单时间
        requiredAddDto.setCreateTime(new Date());
        // 单据状态
        requiredAddDto.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
        // 确认时间
        requiredAddDto.setConfirmTime(null);
        // 审核状态
        requiredAddDto.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue());
        // 表体列表
        if (CollectionUtils.isNotEmpty(contractIds)) {
            List<BizIAuxmatBuyContractList> notNotifiedBuyContractBodyList = this.bizIAuxmatBuyContractListMapper
                    .getNotNotifiedListByHeadIds(contractIds, userInfo.getCompany());
            List<OrderNoticeListDto> orderNoticeBodyDtoList = new ArrayList<>(notNotifiedBuyContractBodyList.size());
            for (BizIAuxmatBuyContractList buyContractList : notNotifiedBuyContractBodyList) {
                OrderNoticeList orderNoticeList = new OrderNoticeList();
                this.copyContractBodyToNoticeBody(buyContractList, orderNoticeList);
                orderNoticeList.setTransportMode(INITIAL_TRANSPORT_MODE);
                orderNoticeList.setPort(INITIAL_PORT);
                orderNoticeList.setRequestDeliveryDate(null);
                OrderNoticeListDto bodyDto = this.orderNoticeListDtoMapper.toDto(orderNoticeList);
                bodyDto.setBuyHeadId(buyContractList.getHeadId());
                orderNoticeBodyDtoList.add(bodyDto);
            }
            requiredAddDto.setBodyList(orderNoticeBodyDtoList.stream()
                    .sorted(Comparator.comparing(OrderNoticeListDto::getProductName))
                    .collect(Collectors.toList()));
        }
        return requiredAddDto;
    }

    /**
     * 新增订货通知
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderNoticeHeadDto insert(OrderNoticeAddParam addParam, UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        List<OrderNoticeListParam> bodyList = addParam.getBodyList();
        List<BizIAuxmatBuyContractList> buyContractBodyList = this.getBuyContractBodyListByIds(bodyList.stream()
                .map(OrderNoticeListParam::getBuyListId).collect(Collectors.toList()), tradeCode);
        // 校验
        if (CollectionUtils.isNotEmpty(buyContractBodyList)) {
            List<String> buyListIds = buyContractBodyList.stream().map(BizIAuxmatBuyContractList::getId).collect(Collectors.toList());
            int geQtyBodyCount = this.orderNoticeListMapper.getGeQtyCountByListIds(buyListIds, tradeCode);
            if (geQtyBodyCount > 0) {
                throw new ErrorException(510, "存在购销合同表体数量耗尽，无法选择，请重新选择购销合同");
            }
        }
        int headCount = this.orderNoticeHeadMapper.getCountByOrderNo(addParam.getOrderNo(), tradeCode);
        if (headCount > 0) {
            throw new ErrorException(500, "订货编号已存在，请重新录入");
        }
        OrderNoticeHead orderNoticeHead = new OrderNoticeHead();
        // 表头业务字段赋值
        orderNoticeHead.setBusinessType(CommonEnum.COMMON_BUSINESS_TYPE_ENUM.STATE_TRADE_IMPORT_ACCESSORIES.getType());
        orderNoticeHead.setOrderNo(addParam.getOrderNo());
        orderNoticeHead.setOrderDate(addParam.getOrderDate());
        orderNoticeHead.setCustomer(addParam.getCustomer());
        orderNoticeHead.setVersionNo(INITIAL_VERSION_NO);
        orderNoticeHead.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
        orderNoticeHead.setNote(addParam.getNote());
        orderNoticeHead.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue());
        BizMerchant bizMerchant = this.bizMerchantMapper.getByMerchantCode(addParam.getCustomer(), tradeCode);
        if (bizMerchant != null && StringUtils.isNotBlank(bizMerchant.getMerchantNameCn())) {
            orderNoticeHead.setCustomerName(bizMerchant.getMerchantNameCn());
        }
        // 表头购销合同关联字段赋值
        List<String> buyHeadIds = addParam.getContractIds();
        List<BizIAuxmatBuyContract> buyContracts = this.getBuyContractsByIds(buyHeadIds);
        if (CollectionUtils.isNotEmpty(buyHeadIds)) {
            String buyHeadIdsStr = String.join(CONTRACT_SEPARATOR, buyHeadIds);
            if (StringUtils.isNotBlank(buyHeadIdsStr)) {
                orderNoticeHead.setBuyHeadIds(buyHeadIdsStr);
            }
            if (CollectionUtils.isNotEmpty(buyContracts)) {
                orderNoticeHead.setPurchaseSalesContractNo(buyContracts.stream()
                        .map(BizIAuxmatBuyContract::getContractNo)
                        .collect(Collectors.joining(CONTRACT_SEPARATOR)));
            }
        }
        // 表头固定字段赋值
        orderNoticeHead.setId(UUID.randomUUID() + "_" + INITIAL_VERSION_NO);
        orderNoticeHead.setTradeCode(tradeCode);
        orderNoticeHead.setCreateBy(userInfo.getUserNo());
        orderNoticeHead.setCreateByName(userInfo.getUserName());
        orderNoticeHead.setCreateTime(new Date());
        orderNoticeHead.setUpdateTime(orderNoticeHead.getCreateTime());
        // 表头入库
        int insertRows = this.orderNoticeHeadMapper.insert(orderNoticeHead);
        OrderNoticeHeadDto headDto = insertRows > 0 ? this.orderNoticeHeadDtoMapper.toDto(orderNoticeHead) : null;
        if (CollectionUtils.isEmpty(buyContractBodyList)) {
            return headDto;
        }
        List<OrderNoticeList> orderNoticeBodyList = new ArrayList<>(buyContractBodyList.size());
        for (BizIAuxmatBuyContractList buyContractBody : buyContractBodyList.stream().sorted(Comparator
                .comparing(BizIAuxmatBuyContractList::getGName)).collect(Collectors.toList())) {
            Optional<OrderNoticeListParam> anyBody = bodyList.stream()
                    .filter(body -> Objects.equals(buyContractBody.getId(), body.getBuyListId()))
                    .findAny();
            if (!anyBody.isPresent()) {
                continue;
            }
            OrderNoticeListParam body = anyBody.get();
            OrderNoticeList orderNoticeList = new OrderNoticeList();
            // 表体业务字段赋值
            this.copyContractBodyToNoticeBody(buyContractBody, orderNoticeList);
            orderNoticeList.setTransportMode(body.getTransportMode());
            orderNoticeList.setPort(body.getPort());
            orderNoticeList.setQty(body.getQty());
            orderNoticeList.setRequestDeliveryDate(body.getRequestDeliveryDate());
            orderNoticeList.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
            // 表体基础字段赋值
            orderNoticeList.setId(UUID.randomUUID() + "_" + INITIAL_VERSION_NO);
            orderNoticeList.setHeadId(orderNoticeHead.getId());
            orderNoticeList.setTradeCode(orderNoticeHead.getTradeCode());
            orderNoticeList.setCreateBy(orderNoticeHead.getCreateBy());
            orderNoticeList.setCreateTime(new Date());
            orderNoticeList.setCreateByName(orderNoticeHead.getCreateByName());
            // 表体购销合同关联字段赋值
            orderNoticeList.setBuyListId(buyContractBody.getId());
            buyContracts.stream()
                    .filter(contract -> Objects.equals(buyContractBody.getHeadId(), contract.getId()))
                    .findAny()
                    .ifPresent(contract -> orderNoticeList.setPurchaseSalesContractNo(contract.getContractNo()));
            orderNoticeBodyList.add(orderNoticeList);
        }
        // 表体入库(放最后，不属同一事务)
        BulkSqlOpt.batchInsertAndThrowException(orderNoticeBodyList, OrderNoticeListMapper.class);
        return headDto;
    }

    /**
     * 修改订货通知表头
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头传输模型
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderNoticeHeadDto update(OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadMapper.selectByPrimaryKey(orderNoticeHeadParam.getId());
        String oldOrderNo = orderNoticeHead.getOrderNo(), oldCustomer = orderNoticeHead.getCustomer(), oldNote = orderNoticeHead.getNote();
        Date oldOrderDate = orderNoticeHead.getOrderDate();
        if (!Objects.equals(oldOrderNo, orderNoticeHeadParam.getOrderNo())) {
            int countByOrderNo = this.orderNoticeHeadMapper.getCountByOrderNo(orderNoticeHeadParam.getOrderNo(), tradeCode);
            if (countByOrderNo > 0) {
                throw new ErrorException(500, "订货编号已存在，请重新录入");
            }
            orderNoticeHead.setOrderNo(orderNoticeHeadParam.getOrderNo());
        }
        orderNoticeHead.setOrderDate(orderNoticeHeadParam.getOrderDate());
        if (!Objects.equals(oldCustomer, orderNoticeHeadParam.getCustomer())) {
            orderNoticeHead.setCustomer(orderNoticeHeadParam.getCustomer());
            BizMerchant bizMerchant = this.bizMerchantMapper.getByMerchantCode(orderNoticeHeadParam.getCustomer(), tradeCode);
            if (bizMerchant != null && StringUtils.isNotBlank(bizMerchant.getMerchantNameCn())) {
                orderNoticeHead.setCustomerName(bizMerchant.getMerchantNameCn());
            }
        }
        orderNoticeHead.setNote(orderNoticeHeadParam.getNote());
        if (Objects.equals(oldOrderNo, orderNoticeHeadParam.getOrderNo())
                && Objects.equals(oldCustomer, orderNoticeHeadParam.getCustomer())
                && Objects.equals(oldNote, orderNoticeHeadParam.getNote())
                && Objects.equals(oldOrderDate, orderNoticeHeadParam.getOrderDate())) {
            return this.orderNoticeHeadDtoMapper.toDto(orderNoticeHead);
        }
        orderNoticeHead.setUpdateTime(new Date());
        orderNoticeHead.setUpdateBy(userInfo.getUserNo());
        orderNoticeHead.setUpdateByName(userInfo.getUserName());
        return this.orderNoticeHeadMapper.updateByPrimaryKey(orderNoticeHead) > 0 ?
                this.orderNoticeHeadDtoMapper.toDto(orderNoticeHead) : null;
    }

    /**
     * 根据主键列表删除
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> ids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<OrderNoticeHead> orderNoticeHeads = this.orderNoticeHeadMapper.getByIds(ids);
        boolean hasAnyNotDraft = orderNoticeHeads.stream()
                .anyMatch(head -> !CommonEnum.STATE_ENUM.DRAFT.getType().equals(head.getDataStatus()));
        if (hasAnyNotDraft) {
            throw new ErrorException(500, "仅编制状态数据允许删除");
        }
        this.orderNoticeHeadMapper.deleteByIds(ids);
        this.orderNoticeListMapper.deleteByHeadIds(ids, userInfo.getCompany());
    }

    /**
     * 获取新增明细数据
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 新增明细数据
     */
    public List<OrderNoticeListDto> getAddListDetailData(OrderNoticeAddParam addParam, UserInfoToken<?> userInfo) {
        List<String> contractIds = addParam.getContractIds();
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }
        List<OrderNoticeList> orderNoticeBodyList = this.orderNoticeListMapper.getRemainUnSelectedData(contractIds, userInfo.getCompany());
        for (OrderNoticeList orderNoticeList : orderNoticeBodyList) {
            orderNoticeList.setTransportMode(INITIAL_TRANSPORT_MODE);
            orderNoticeList.setPort(INITIAL_PORT);
            orderNoticeList.setRequestDeliveryDate(null);
        }
        return orderNoticeBodyList.stream()
                .map(body -> {
                    OrderNoticeListDto dto = this.orderNoticeListDtoMapper.toDto(body);
                    // 赋值购销合同表头id
                    dto.setBuyHeadId(dto.getHeadId());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 新增明细
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void addListDetail(OrderNoticeAddParam addParam, UserInfoToken<?> userInfo) {
        String id = addParam.getId(), tradeCode = userInfo.getCompany();
        List<String> contractIds = addParam.getContractIds(), contractListIds = addParam.getContractListIds();
        if (CollectionUtils.isEmpty(contractListIds) || CollectionUtils.isEmpty(contractIds)) {
            return;
        }
        // 校验
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadMapper.selectByPrimaryKey(id);
        if (orderNoticeHead == null) {
            throw new ErrorException(500, "当前订货通知数据不存在");
        }
        int bodyCount = this.orderNoticeListMapper.getGeQtyCountByListIds(contractListIds, tradeCode);
        if (bodyCount > 0) {
            throw new ErrorException(510, "存在购销合同表体数量耗尽，无法选择，请刷新重试");
        }
        List<BizIAuxmatBuyContractList> buyContractBodyList = this.getBuyContractBodyListByIds(contractListIds, tradeCode);
        final Map<String, Integer> countTable = new HashMap<>();
        buyContractBodyList.forEach(body -> {
            if (StringUtils.isNotBlank(body.getGName())) {
                countTable.merge(body.getGName(), 1, Integer::sum);
            }
        });
        countTable.forEach((gName, count) -> {
            if (count > 1) {
                throw new ErrorException(500, "商品名称为 " + gName + " 的明细重复选择");
            }
        });
        List<BizIAuxmatBuyContract> buyContracts = this.getBuyContractsByIds(contractIds);
        List<OrderNoticeList> currentNoticeBodyList = this.orderNoticeListMapper.getByHeadIds(
                Collections.singletonList(id), tradeCode);
        List<String> productNames = currentNoticeBodyList.stream()
                .map(OrderNoticeList::getProductName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Optional<BizIAuxmatBuyContractList> anyEqualGName = buyContractBodyList.stream()
                .filter(body -> productNames.contains(body.getGName())).findAny();
        if (anyEqualGName.isPresent()) {
            String gName = anyEqualGName.get().getGName();
            throw new ErrorException(500, "商品名称为 " + gName + " 的表体已存在");
        }
        // 入库
        List<OrderNoticeList> orderNoticeBodyList = new ArrayList<>(buyContractBodyList.size());
        for (BizIAuxmatBuyContractList buyContractList : buyContractBodyList) {
            OrderNoticeList orderNoticeList = new OrderNoticeList();
            // 业务字段赋值
            this.copyContractBodyToNoticeBody(buyContractList, orderNoticeList);
            orderNoticeList.setPort(INITIAL_PORT);
            orderNoticeList.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
            orderNoticeList.setTransportMode(INITIAL_TRANSPORT_MODE);
            // 表体基础字段赋值
            orderNoticeList.setId(UUID.randomUUID().toString());
            orderNoticeList.setHeadId(orderNoticeHead.getId());
            orderNoticeList.setTradeCode(tradeCode);
            orderNoticeList.setCreateBy(userInfo.getUserNo());
            orderNoticeList.setCreateTime(new Date());
            orderNoticeList.setCreateByName(userInfo.getUserName());
            // 表体购销合同关联字段赋值
            orderNoticeList.setBuyListId(buyContractList.getId());
            buyContracts.stream()
                    .filter(contract -> Objects.equals(buyContractList.getHeadId(), contract.getId()))
                    .findAny()
                    .ifPresent(contract -> orderNoticeList.setPurchaseSalesContractNo(contract.getContractNo()));
            orderNoticeBodyList.add(orderNoticeList);
        }
        // 表体入库(放最后，不属同一事务)
        BulkSqlOpt.batchInsertAndThrowException(orderNoticeBodyList, OrderNoticeListMapper.class);
    }

    /**
     * 订货通知确认
     *
     * @param id       主键
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderNoticeHeadDto confirm(String id, UserInfoToken<?> userInfo) {
        OrderNoticeHead noticeHead = this.orderNoticeHeadMapper.selectByPrimaryKey(id);
        if (CommonEnum.STATE_ENUM.CONFIRMED.getType().equals(noticeHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已确认，无需重复操作");
        }
        if (CommonEnum.STATE_ENUM.INVALID.getType().equals(noticeHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已作废，不允许确认");
        }
        this.orderNoticeHeadMapper.confirmById(id, userInfo.getUserNo(), userInfo.getUserName());
        this.orderNoticeListMapper.confirmByHeadId(id, userInfo.getUserNo(), userInfo.getUserName());
        OrderNoticeHead newNoticeHead = this.orderNoticeHeadMapper.selectByPrimaryKey(id);
        return this.orderNoticeHeadDtoMapper.toDto(newNoticeHead);
    }

    /**
     * 订货通知作废
     *
     * @param id       主键
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void invalidate(String id, UserInfoToken<?> userInfo) {
        OrderNoticeHead noticeHead = this.orderNoticeHeadMapper.selectByPrimaryKey(id);
        if (CommonEnum.STATE_ENUM.INVALID.getType().equals(noticeHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已作废，无需重复操作");
        }
        this.orderNoticeHeadMapper.invalidateById(id, userInfo.getUserNo(), userInfo.getUserName());
        this.orderNoticeListMapper.invalidateByHeadId(id, userInfo.getUserNo(), userInfo.getUserName());
    }

    /**
     * 校验版本复制
     *
     * @param orderNo  订货编号
     * @param userInfo 用户信息
     * @return 是否存在有效数据
     */
    public String checkVersionCopy(String orderNo, UserInfoToken<?> userInfo) {
        List<OrderNoticeHead> orderNoticeHeadList = this.orderNoticeHeadMapper.getByOrderNo(orderNo, userInfo.getCompany());
        if (CollectionUtils.isEmpty(orderNoticeHeadList)) {
            return CommonVariable.NO_CODE;
        }
        boolean hasValidData = orderNoticeHeadList.stream()
                .anyMatch(head -> !CommonEnum.STATE_ENUM.INVALID.getType().equals(head.getDataStatus()));
        return hasValidData ? CommonVariable.YES_CODE : CommonVariable.NO_CODE;
    }

    /**
     * 版本复制
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void versionCopy(OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        OrderNoticeHead noticeHead = this.orderNoticeHeadMapper.selectByPrimaryKey(orderNoticeHeadParam.getId());
        if (noticeHead == null) {
            throw new ErrorException(500, "辅料订货通知不存在，请刷新");
        }
        if (!Objects.equals(tradeCode, noticeHead.getTradeCode())) {
            throw new ErrorException(500, "企业编码不匹配，无法操作当前数据");
        }
        // 查询最大版本号、下一个版本号
        OrderNoticeHead maxVersionNoticeHead = this.orderNoticeHeadMapper.getMaxVersionDataByOrderNo(noticeHead.getOrderNo(), tradeCode);
        int nextVersionNo = Integer.parseInt(maxVersionNoticeHead.getVersionNo()) + 1;
        // 作废当前订货编号数据
        this.orderNoticeHeadMapper.invalidateByOrderNo(noticeHead.getOrderNo(), tradeCode);
        this.orderNoticeListMapper.invalidateByOrderNo(noticeHead.getOrderNo(), tradeCode);
        // 复制表头
        OrderNoticeHead newNoticeHead = new OrderNoticeHead();
        BeanUtils.copyProperties(noticeHead, newNoticeHead);
        newNoticeHead.setId(UUID.randomUUID() + "_" + nextVersionNo);
        newNoticeHead.setVersionNo(String.valueOf(nextVersionNo));
        newNoticeHead.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
        newNoticeHead.setConfirmTime(null);
        newNoticeHead.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue());
        newNoticeHead.setCreateTime(new Date());
        newNoticeHead.setCreateBy(userInfo.getUserNo());
        newNoticeHead.setCreateByName(userInfo.getUserName());
        newNoticeHead.setUpdateBy(null);
        newNoticeHead.setUpdateByName(null);
        newNoticeHead.setUpdateTime(newNoticeHead.getCreateTime());
        newNoticeHead.setParentId(noticeHead.getId());
        this.orderNoticeHeadMapper.insert(newNoticeHead);
        // 复制表体
        List<OrderNoticeList> noticeBodyList = this.orderNoticeListMapper
                .getByHeadIdsAndNotSelected(Collections.singletonList(noticeHead.getId()), tradeCode)
                .stream()
                .sorted(Comparator.comparing(OrderNoticeList::getCreateTime).reversed())
                .collect(Collectors.toList());
        List<OrderNoticeList> newNoticeBodyList = new ArrayList<>(noticeBodyList.size());
        for (OrderNoticeList noticeList : noticeBodyList) {
            OrderNoticeList newNoticeList = new OrderNoticeList();
            BeanUtils.copyProperties(noticeList, newNoticeList);
            newNoticeList.setId(UUID.randomUUID() + "_" + nextVersionNo);
            newNoticeList.setHeadId(newNoticeHead.getId());
            newNoticeList.setConfirmTime(null);
            newNoticeList.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
            newNoticeList.setUpdateByName(null);
            newNoticeList.setUpdateTime(null);
            newNoticeList.setUpdateBy(null);
            newNoticeList.setCreateBy(userInfo.getUserNo());
            newNoticeList.setCreateTime(new Date());
            newNoticeList.setCreateByName(userInfo.getUserName());
            newNoticeList.setParentId(noticeList.getId());
            newNoticeBodyList.add(newNoticeList);
        }
        // 复制归档附件
        List<Attached> attachedList = this.attachedMapper.getByHeadId(noticeHead.getId(), tradeCode);
        if (CollectionUtils.isNotEmpty(attachedList)) {
            List<Attached> tempAttachList = new ArrayList<>();
            for (Attached attached : attachedList) {
                attached.setSid(UUID.randomUUID().toString());
                attached.setBusinessSid(newNoticeHead.getId());
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制辅料订货通知表头【" + noticeHead.getOrderNo() + "】归档文件！");
                String oldFileName = attached.getFileName();
                try {
                    byte[] bytes = oldFileName.startsWith("TIANYI") ? fileHandler.downloadFile(oldFileName)
                            : otherFileHandler.downloadFile(oldFileName);
                    String newFileName = fileHandler.uploadFile(bytes, oldFileName);
                    tempAttachList.add(attached);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            RoundingMode.HALF_UP));
                    this.attachedMapper.insert(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}", tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }
        // 表体入库(放最后，不属同一事务)
        if (CollectionUtils.isNotEmpty(newNoticeBodyList)) {
            BulkSqlOpt.batchInsertAndThrowException(newNoticeBodyList, OrderNoticeListMapper.class);
        }
    }

    /**
     * 复制购销合同表体数据至订货通知表体
     *
     * @param buyContractBody 购销合同表体
     * @param orderNoticeList 订货通知表体
     */
    private void copyContractBodyToNoticeBody(BizIAuxmatBuyContractList buyContractBody, OrderNoticeList orderNoticeList) {
        if (orderNoticeList == null) {
            return;
        }
        orderNoticeList.setId(buyContractBody.getId());
        orderNoticeList.setBuyListId(buyContractBody.getId());
        orderNoticeList.setProductName(buyContractBody.getGName());
        orderNoticeList.setProductModel(buyContractBody.getGModel());
        orderNoticeList.setSpecification(buyContractBody.getSpecifications());
        orderNoticeList.setWeight(buyContractBody.getGramWeight());
        orderNoticeList.setSupplier(buyContractBody.getSupplier());
        orderNoticeList.setQty(buyContractBody.getQty());
        orderNoticeList.setUnit(buyContractBody.getUnit());
        orderNoticeList.setUnitPrice(buyContractBody.getUnitPrice());
    }

    /**
     * 根据主键列表获取购销合同
     *
     * @param contractIds 主键列表
     * @return 购销合同列表
     */
    private List<BizIAuxmatBuyContract> getBuyContractsByIds(List<String> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }
        Example example = new Example(BizIAuxmatBuyContract.class);
        example.createCriteria().andIn("id", contractIds);
        return bizIAuxmatBuyContractMapper.selectByExample(example);
    }

    /**
     * 根据主键列表获取购销合同表体列表
     *
     * @param contractBodyIds 主键列表
     * @param tradeCode       企业编码
     * @return 购销合同表体列表
     */
    private List<BizIAuxmatBuyContractList> getBuyContractBodyListByIds(List<String> contractBodyIds, String tradeCode) {
        if (CollectionUtils.isEmpty(contractBodyIds)) {
            return Collections.emptyList();
        }
        Example example = new Example(BizIAuxmatBuyContractList.class);
        example.createCriteria().andIn("id", contractBodyIds).andEqualTo("tradeCode", tradeCode);
        return bizIAuxmatBuyContractListMapper.selectByExample(example);
    }

    /**
     * 打印采购订单表
     *
     * @param ids      主键列表
     * @param type     类型 xlsx or pdf
     * @param userInfo 用户信息
     * @return 采购订单表文件数据
     */
    public byte[] printOrderForm(List<String> ids, String type, UserInfoToken<?> userInfo) throws IOException {
        if (CollectionUtils.isEmpty(ids)) {
            return new byte[0];
        }
        boolean isPdf = PDF_TYPE.equals(type), isSingle = ids.size() == 1;
        // 创建文件夹
        String flagId = UUID.randomUUID().toString();
        String fileDirName = flagId + DIR + "-" + RandomUtils.nextInt(1, 1000);
        String pdfDirName = fileDirName + "-" + PDF_TYPE;
        FileUtils.forceMkdir(new File(this.tempPath + fileDirName));
        if (isPdf) {
            FileUtils.forceMkdir(new File(this.tempPath + pdfDirName));
        }
        // 生成文件
        List<String> filePaths = new ArrayList<>(ids.size());
        String tradeCode = userInfo.getCompany();
        List<OrderNoticeHead> heads = this.orderNoticeHeadMapper.getByIds(ids);
        List<OrderNoticeList> bodys = this.orderNoticeListMapper.getByHeadIds(ids, tradeCode);
        List<String> suppliers = bodys.stream().map(OrderNoticeList::getSupplier)
                .filter(StringUtils::isNotBlank).
                collect(Collectors.toList());
        List<String> gNames = bodys.stream().map(OrderNoticeList::getProductName)
                .filter(StringUtils::isNotBlank).
                collect(Collectors.toList());
        List<BizMerchant> bizMerchants = CollectionUtils.isEmpty(suppliers) ? Collections.emptyList()
                : this.bizMerchantMapper.getByMerchantCodes(suppliers, tradeCode);
        List<BizQuotation> quotations = CollectionUtils.isEmpty(gNames) ? Collections.emptyList()
                : this.bizQuotationMapper.getByGNames(gNames, tradeCode);
        for (OrderNoticeHead head : heads) {
            List<OrderNoticeList> bodyList = bodys.stream()
                    .filter(body -> Objects.equals(head.getId(), body.getHeadId()))
                    .collect(Collectors.toList());
            PurchaseOrderFormDto.Head formHead = new PurchaseOrderFormDto.Head()
                    .setOrderNo(head.getOrderNo())
                    .setCurrentDate(DateUtils.dateToString(new Date(), "yyyy.MM.dd"))
                    .setCreateDate(DateUtils.dateToString(head.getCreateTime(), "yyyy.MM.dd"))
                    .setCreateByName(head.getCreateByName());
            List<PurchaseOrderFormDto.Body> formBodyList = new ArrayList<>(bodyList.size());
            if (CollectionUtils.isNotEmpty(bodyList)) {
                for (OrderNoticeList body : bodys) {
                    PurchaseOrderFormDto.Body formBody = new PurchaseOrderFormDto.Body()
                            .setProductName(body.getProductName())
                            .setProductModel(body.getProductModel())
                            .setSpecifications(body.getSpecification())
                            .setUnit(body.getUnit())
                            .setTransportMode(CommonEnum.TRANSPORT_MODE.getValue(body.getTransportMode()))
                            .setUnitPrice(StringUtils.EMPTY)
                            .setPort(Optional.ofNullable(this.pCodeHolder.getValue(PCodeType.PORT_LIN,
                                    body.getPort())).orElse(body.getPort()))
                            .setFinalUser(PRINT_FIX_FINAL_USER)
                            .setApplicantCompany(PRINT_FIX_APP_COMPANY)
                            .setOrderNo(head.getOrderNo())
                            .setIsNew(PRINT_FIX_IS_NEW);
                    if (body.getQty() != null) {
                        formBody.setQty(NumberFormatterUtils.formatNumber(body.getQty()));
                    }
                    if (body.getWeight() != null) {
                        formBody.setWeight(NumberFormatterUtils.formatNumber(body.getWeight()));
                    }
                    if (body.getRequestDeliveryDate() != null) {
                        formBody.setArrivedTime(DateUtils.dateToString(body.getRequestDeliveryDate(), "yyyy/MM/dd"));
                    }
                    if (StringUtils.isNotBlank(body.getSupplier())) {
                        String supplierName = bizMerchants.stream()
                                .filter(bizMerchant -> Objects.equals(bizMerchant.getMerchantCode()
                                        , body.getSupplier()))
                                .map(BizMerchant::getMerchantNameCn)
                                .filter(StringUtils::isNotBlank)
                                .findAny()
                                .orElse(StringUtils.EMPTY);
                        formBody.setSupplier(supplierName);
                    }
                    if (StringUtils.isNotBlank(body.getProductName())) {
                        String materialNo = quotations.stream().filter(quotation -> Objects.equals(quotation.getGName()
                                        , body.getProductName()))
                                .map(BizQuotation::getMaterialNo)
                                .filter(StringUtils::isNotBlank)
                                .findAny()
                                .orElse(StringUtils.EMPTY);
                        formBody.setMaterialNo(materialNo);
                    }
                    formBodyList.add(formBody);
                }
            }
            String fileRelativePath = fileDirName + File.separator + (isSingle ? PRINT_FILE_NAME : PRINT_FILE_NAME
                    + head.getOrderNo()) + ".xlsx";
            String filePath = this.exportService.export(Collections.singletonList(formHead), formBodyList,
                    fileRelativePath, PRINT_TEMPLATE_NAME);
            if (isPdf) {
                PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
                pdfSaveOptions.setOnePagePerSheet(true);
                byte[] pdfBytes = ExportService.pdfSetTitle(ExportService.excelToPdf(IOUtils.
                                toByteArray(Files.newInputStream(Paths.get(filePath))), pdfSaveOptions)
                        , PRINT_FILE_NAME + ".pdf");
                String pdfPath = this.tempPath + File.separator + pdfDirName + File.separator +
                        (isSingle ? PRINT_FILE_NAME : PRINT_FILE_NAME + head.getOrderNo()) + ".pdf";
                IOUtils.write(pdfBytes, Files.newOutputStream(Paths.get(pdfPath)));
                filePaths.add(pdfPath);
            } else {
                filePaths.add(filePath);
            }
        }
        if (CollectionUtils.isEmpty(filePaths)) {
            return new byte[0];
        }
        if (isSingle) {
            return IOUtils.toByteArray(Files.newInputStream(Paths.get(filePaths.get(0))));
        }
        FileUtil.fileToZip(this.tempPath + (isPdf ? pdfDirName : fileDirName), this.tempPath, flagId);
        return IOUtils.toByteArray(Files.newInputStream(Paths.get(this.tempPath + flagId + ".zip")));
    }
}