package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* BizINonStateAuxmatAggrContractHead
* <AUTHOR>
* @date: 2025-6-18
*/
public interface BizINonStateAuxmatAggrContractHeadMapper extends Mapper<BizINonStateAuxmatAggrContractHead> {
    /**
     * 查询获取数据
     * @param bizINonStateAuxmatAggrContractHead
     * @return
     */
    List<BizINonStateAuxmatAggrContractHead> getList(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead);
    List<BizINonStateAuxmatAggrContractHead> getListPagedToCustomerAccount(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    BizINonStateAuxmatAggrContractHead selectByContractNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    int checkContractId(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead);

    BizINonStateAuxmatAggrContractHead getMaxVersionNoByContractNo(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead);



    void updateCancelByContract(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据业务主键获取随附件的文件信息
     * @param headId 业务主键 订单表头的sid
     * @return 返回附件信息集合
     */
    List<Attached> getAttachmentFile(@Param("headId") String headId);
    /**
     * 校验是否存在 同一个购销合同号是否存在未作废的数据
     * @param id 合同表头id
     * @return 返回订单号集合
     */
    List<String> checkContractIdNotCancel(@Param("id") String id);

    int checkOrderUsed(@Param("sids") List<String> sids);

    /**
     * 计算表头金额相关字段
     * @param headId 表头ID
     * @return 计算后的表头对象
     */
    BizINonStateAuxmatAggrContractHead calculateContractAmounts(@Param("headId") String headId, @Param("exchangeRate") BigDecimal exchangeRate, @Param("billingWeight") BigDecimal billingWeight, @Param("paymentAmount") BigDecimal paymentAmount);

    void saveTransferNotice(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead);


}
