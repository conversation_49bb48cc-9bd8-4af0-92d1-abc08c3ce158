<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractListMapper">
    <resultMap id="bizINonStateAuxmatAggrContractListResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList">
		<id column="ID" property="id" jdbcType="VARCHAR" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
		<result column="GOODS_DESC" property="goodsDesc" jdbcType="VARCHAR" />
		<result column="QTY" property="qty" jdbcType="NUMERIC" />
		<result column="UNIT" property="unit" jdbcType="VARCHAR" />
		<result column="UNIT_PRICE" property="unitPrice" jdbcType="NUMERIC" />
		<result column="AMOUNT" property="amount" jdbcType="NUMERIC" />
		<result column="DELIVERY_DATE" property="deliveryDate" jdbcType="TIMESTAMP" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
		<result column="GOODS_CATEGORY" property="goodsCategory" jdbcType="VARCHAR" />
		<result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     ID
     ,HEAD_ID
     ,CREATE_BY
     ,CREATE_TIME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,SYS_ORG_CODE
     ,TRADE_CODE
     ,GOODS_NAME
     ,GOODS_DESC
     ,QTY
     ,UNIT
     ,UNIT_PRICE
     ,AMOUNT
     ,DELIVERY_DATE
     ,REMARK
     ,GOODS_CATEGORY
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
    </sql>
    <sql id="condition">
    <if test="headId != null and headId != ''">
        and t.HEAD_ID = #{headId}
    </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizINonStateAuxmatAggrContractListResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getBizIAuxmatForContractListByHeadid"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList">
        select
        <include refid="Base_Column_List"></include>
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST t
        where t.head_id = #{headId}
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST t where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getContractListByHeadId"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST t
        <where>
            head_id = #{id}
        </where>
        ORDER BY t.unit_price ASC
    </select>
</mapper>
