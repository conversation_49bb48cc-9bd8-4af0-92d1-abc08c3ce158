package com.dcjet.cs.auxiliaryMaterials.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizINonStateAuxmatAggrContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Service
public class BizINonStateAuxmatAggrContractListService extends BaseService<BizINonStateAuxmatAggrContractList> {
    @Resource
    private BizINonStateAuxmatAggrContractListMapper bizINonStateAuxmatAggrContractListMapper;
    @Resource
    private BizINonStateAuxmatAggrContractListDtoMapper bizINonStateAuxmatAggrContractListDtoMapper;
    @Override
    public Mapper<BizINonStateAuxmatAggrContractList> getMapper() {
        return bizINonStateAuxmatAggrContractListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizINonStateAuxmatAggrContractListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizINonStateAuxmatAggrContractListDto>> getListPaged(BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, PageParam pageParam) {
        // 启用分页查询
        BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList = bizINonStateAuxmatAggrContractListDtoMapper.toPo(bizINonStateAuxmatAggrContractListParam);
        Page<BizINonStateAuxmatAggrContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizINonStateAuxmatAggrContractListMapper.getList(bizINonStateAuxmatAggrContractList));
        List<BizINonStateAuxmatAggrContractListDto> bizINonStateAuxmatAggrContractListDtos = page.getResult().stream().map(head -> {
            BizINonStateAuxmatAggrContractListDto dto = bizINonStateAuxmatAggrContractListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizINonStateAuxmatAggrContractListDto>> paged = ResultObject.createInstance(bizINonStateAuxmatAggrContractListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizINonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizINonStateAuxmatAggrContractListDto insert(BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList = bizINonStateAuxmatAggrContractListDtoMapper.toPo(bizINonStateAuxmatAggrContractListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizINonStateAuxmatAggrContractList.setId(sid);
        bizINonStateAuxmatAggrContractList.setCreateBy(userInfo.getUserNo());
        bizINonStateAuxmatAggrContractList.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizINonStateAuxmatAggrContractListMapper.insert(bizINonStateAuxmatAggrContractList);
        return  insertStatus > 0 ? bizINonStateAuxmatAggrContractListDtoMapper.toDto(bizINonStateAuxmatAggrContractList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizINonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizINonStateAuxmatAggrContractListDto update(BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList = bizINonStateAuxmatAggrContractListMapper.selectByPrimaryKey(bizINonStateAuxmatAggrContractListParam.getId());
        bizINonStateAuxmatAggrContractListDtoMapper.updatePo(bizINonStateAuxmatAggrContractListParam, bizINonStateAuxmatAggrContractList);
        bizINonStateAuxmatAggrContractList.setUpdateBy(userInfo.getUserNo());
        bizINonStateAuxmatAggrContractList.setUpdateTime(new Date());
        // 更新数据
        int update = bizINonStateAuxmatAggrContractListMapper.updateByPrimaryKey(bizINonStateAuxmatAggrContractList);
        return update > 0 ? bizINonStateAuxmatAggrContractListDtoMapper.toDto(bizINonStateAuxmatAggrContractList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizINonStateAuxmatAggrContractListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizINonStateAuxmatAggrContractListDto> selectAll(BizINonStateAuxmatAggrContractListParam exportParam, UserInfoToken userInfo) {
        BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList = bizINonStateAuxmatAggrContractListDtoMapper.toPo(exportParam);
        // bizINonStateAuxmatAggrContractList.setTradeCode(userInfo.getCompany());
        List<BizINonStateAuxmatAggrContractListDto> bizINonStateAuxmatAggrContractListDtos = new ArrayList<>();
        List<BizINonStateAuxmatAggrContractList> bizINonStateAuxmatAggrContractLists = bizINonStateAuxmatAggrContractListMapper.getList(bizINonStateAuxmatAggrContractList);
        if (CollectionUtils.isNotEmpty(bizINonStateAuxmatAggrContractLists)) {
            bizINonStateAuxmatAggrContractListDtos = bizINonStateAuxmatAggrContractLists.stream().map(head -> {
                BizINonStateAuxmatAggrContractListDto dto = bizINonStateAuxmatAggrContractListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizINonStateAuxmatAggrContractListDtos;
    }
}
