package com.dcjet.cs.auxiliaryMaterials.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Setter
@Getter
@Table(name = "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST")
public class BizINonStateAuxmatAggrContractList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	 @Id
	@Column(name = "ID")
	private  String id;
	/**
     * 表头ID
     */
	@Column(name = "HEAD_ID")
	private  String headId;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     *  修改人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 商品名称
     */
	@Column(name = "GOODS_NAME")
	private  String goodsName;
	/**
     * 商品描述
     */
	@Column(name = "GOODS_DESC")
	private  String goodsDesc;
	/**
     * 数量
     */
	@Column(name = "QTY")
	private  BigDecimal qty;
	/**
     * 单位
     */
	@Column(name = "UNIT")
	private  String unit;
	/**
     * 单价
     */
	@Column(name = "UNIT_PRICE")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Column(name = "AMOUNT")
	private  BigDecimal amount;
	/**
     * 交货日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "DELIVERY_DATE")
	private  Date deliveryDate;
	/**
     * 备注
     */
	@Column(name = "REMARK")
	private  String remark;
	/**
     * 商品类别
     */
	@Column(name = "GOODS_CATEGORY")
	private  String goodsCategory;
	/**
     * 插入用户名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 更新用户名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "EXTEND10")
	private  String extend10;
}
