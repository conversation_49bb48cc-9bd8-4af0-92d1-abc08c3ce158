package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead;
import com.dcjet.cs.importedCigarettes.model.BizIPlanList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIAuxmatBuyContractList
* <AUTHOR>
* @date: 2025-5-29
*/
public interface BizIAuxmatBuyContractListMapper extends Mapper<BizIAuxmatBuyContractList> {
    /**
     * 查询获取数据
     * @param bizIAuxmatBuyContractList
     * @return
     */
    List<BizIAuxmatBuyContractList> getList(BizIAuxmatBuyContractList bizIAuxmatBuyContractList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<BizIAuxmatBuyContractList> getContractListByHeadId(@Param("id") String id);

    /**
     * 根据headId列表获取未订货通知的购销合同表体列表
     *
     * @param headIds   表头主键列表
     * @param tradeCode 企业编码
     * @return 购销合同表体列表
     */
    List<BizIAuxmatBuyContractList> getNotNotifiedListByHeadIds(@Param("headIds") List<String> headIds
            , @Param("tradeCode") String tradeCode);
}
