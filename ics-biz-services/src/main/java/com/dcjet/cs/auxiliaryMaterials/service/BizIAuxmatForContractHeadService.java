package com.dcjet.cs.auxiliaryMaterials.service;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatForContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.dec.dao.BizIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizIncomingGoodsListMapper;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import com.xdo.domain.KeyValuePair;
import com.xdo.file.XdoFileHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatForContractHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Slf4j
@Service
public class BizIAuxmatForContractHeadService extends BaseService<BizIAuxmatForContractHead> {
    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;
    @Resource
    private BizIAuxmatForContractListMapper bizIAuxmatForContractListMapper;
    @Resource
    private BizIAuxmatForContractListDtoMapper bizIAuxmatForContractListDtoMapper;
    @Resource
    private BizIAuxmatForContractHeadMapper bizIAuxmatForContractHeadMapper;
    @Resource
    private BizIAuxmatForContractHeadDtoMapper bizIAuxmatForContractHeadDtoMapper;
    @Resource
    private BizIncomingGoodsHeadMapper bizIncomingGoodsHeadMapper;
    @Resource
    private BizIncomingGoodsListMapper bizIncomingGoodsListMapper;
    @Resource
    private AttachedService attachedService;
    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;

    @Override
    public Mapper<BizIAuxmatForContractHead> getMapper() {
        return bizIAuxmatForContractHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIAuxmatForContractHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIAuxmatForContractHeadDto>> getListPaged(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(bizIAuxmatForContractHeadParam);
        Page<BizIAuxmatForContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatForContractHeadMapper.getList(bizIAuxmatForContractHead));
        List<BizIAuxmatForContractHeadDto> bizIAuxmatForContractHeadDtos = page.getResult().stream().map(head -> {
            BizIAuxmatForContractHeadDto dto = bizIAuxmatForContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIAuxmatForContractHeadDto>> paged = ResultObject.createInstance(bizIAuxmatForContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatForContractHeadDto insert(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(bizIAuxmatForContractHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIAuxmatForContractHead.setId(sid);
        bizIAuxmatForContractHead.setCreateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setCreateTime(new Date());
        bizIAuxmatForContractHead.setTradeCode(userInfo.getCompany());
        bizIAuxmatForContractHead.setInsertUserName(userInfo.getUserName());
        //生成对应表体数据
        List<String> orderNoList = bizIAuxmatForContractHeadParam.getOrderNo();
        if (CollectionUtils.isEmpty(orderNoList)) {
            throw new ErrorException(400, "请选择需要新增数据！");
        }
        bizIAuxmatForContractListMapper.insertByOrderNoList(orderNoList, sid, userInfo.getUserNo(), userInfo.getUserName());
        //获取购销合同
        String orderNo = bizIAuxmatForContractListMapper.getPurchaseSalesContractNo(orderNoList);
        bizIAuxmatForContractHead.setExtend1(orderNo);
        //设置固定字段
        bizIAuxmatForContractHead.setSupplierName(orderNoList.get(0).split("_")[1]);
        bizIAuxmatForContractHead.setSignDate(new Date());
        bizIAuxmatForContractHead.setBusinessType(CommonEnum.businessTypeEnum.type_2.getCode());
        bizIAuxmatForContractHead.setDocStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizIAuxmatForContractHead.setAuditStatus(CommonEnum.APPR_STAUTS_ENUM.APPR_STAUTS_0.getCode());
        bizIAuxmatForContractHead.setVersionNo("1");
        bizIAuxmatForContractHead.setCurrency("USD");
        bizIAuxmatForContractHead.setCustomsPort("CHN331");
        //获取中国烟草国际有限公司 code
        String buyerCode = bizIContractHeadMapper.getBuyerCodeByName("中国烟草国际有限公司", userInfo.getCompany());
        if (StringUtils.isNotBlank(buyerCode)) {
            bizIAuxmatForContractHead.setCustomerName(buyerCode);
        }else {
            bizIAuxmatForContractHead.setCustomerName("中国烟草国际有限公司");
        }
        // 新增数据
        int insertStatus = bizIAuxmatForContractHeadMapper.insert(bizIAuxmatForContractHead);
        return  insertStatus > 0 ? bizIAuxmatForContractHeadDtoMapper.toDto(bizIAuxmatForContractHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatForContractHeadDto update(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getSid());
        //校验合同号唯一
        if (bizIAuxmatForContractHeadMapper.checkContractNo(bizIAuxmatForContractHeadParam.getContractNo(),bizIAuxmatForContractHeadParam.getId()) > 0){
            throw new ErrorException(400, "合同号已存在，请重新输入！");
        }
        bizIAuxmatForContractHeadDtoMapper.updatePo(bizIAuxmatForContractHeadParam, bizIAuxmatForContractHead);
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return update > 0 ? bizIAuxmatForContractHeadDtoMapper.toDto(bizIAuxmatForContractHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        //校验数据状态
        if (bizIAuxmatForContractHeadMapper.checkCanDelBySids(sids) > 0){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }
		bizIAuxmatForContractHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIAuxmatForContractHeadDto> selectAll(BizIAuxmatForContractHeadParam exportParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(exportParam);
        // bizIAuxmatForContractHead.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatForContractHeadDto> bizIAuxmatForContractHeadDtos = new ArrayList<>();
        List<BizIAuxmatForContractHead> bizIAuxmatForContractHeads = bizIAuxmatForContractHeadMapper.getList(bizIAuxmatForContractHead);
        if (CollectionUtils.isNotEmpty(bizIAuxmatForContractHeads)) {
            bizIAuxmatForContractHeadDtos = bizIAuxmatForContractHeads.stream().map(head -> {
                BizIAuxmatForContractHeadDto dto = bizIAuxmatForContractHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIAuxmatForContractHeadDtos;
    }

    /**
     * 确认数据状态
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmDataStatus(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("确认成功"));
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getId());
        if (bizIAuxmatForContractHead == null) {
            throw new ErrorException(400, "辅料合同数据不存在，请刷新");
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizIAuxmatForContractHead.getDocStatus())){
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizIAuxmatForContractHead.getDocStatus())){
            throw new ErrorException(400, "该数据已作废，不允许确认");
        }
        bizIAuxmatForContractHead.setDocStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        bizIAuxmatForContractHead.setConfirmDate(new Date());
        bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return result;
    }

    /**
     * 发送审核
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendAudit(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("发送审核成功"));
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getId());
        if (bizIAuxmatForContractHead == null) {
            throw new ErrorException(400, "辅料合同数据不存在，请刷新");
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizIAuxmatForContractHead.getDocStatus())){
            throw new ErrorException(400, "只有数据状态为1确认的数据允许操作发送审批");
        }
        if (!CommonEnum.OrderAuditStatusEnum.NOT_INVOLVED.getValue().equals(bizIAuxmatForContractHead.getAuditStatus())){
            throw new ErrorException(400, "只有未审核数据允许操作发送审批");
        }
        bizIAuxmatForContractHead.setAuditStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return result;
    }

    /**
     * 作废数据状态
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject cancelDataStatus(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("作废成功"));
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getId());
        if (bizIAuxmatForContractHead == null) {
            throw new ErrorException(400, "辅料合同数据不存在，请刷新");
        }
        if (CommonEnum.OrderAuditStatusEnum.NOT_APPROVED.getValue().equals(bizIAuxmatForContractHead.getAuditStatus())){
            throw new ErrorException(400, "审批中的数据不允许作废");
        }
        // 校验合同号是否在下游数据使用
        if (bizIAuxmatForContractHeadMapper.checkContractIsUsed(bizIAuxmatForContractHead.getContractNo()) > 0){
            throw new ErrorException(400, "进货管理数据存在此合同号,不允许作废");
        }
        bizIAuxmatForContractHead.setDocStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return result;
    }

    public ResultObject<List<BizIAuxmatForContractListDto>> getPlanListPaged(BizIAuxmatForContractListParam bizIAuxmatForContractListParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatForContractList bizIAuxmatForContractList = bizIAuxmatForContractListDtoMapper.toPo(bizIAuxmatForContractListParam);
        Page<BizIAuxmatForContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatForContractListMapper.getPlanListPaged(bizIAuxmatForContractList));
        List<BizIAuxmatForContractListDto> bizIAuxmatForContractListDtos = page.getResult().stream().map(head -> {
            BizIAuxmatForContractListDto dto = bizIAuxmatForContractListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizIAuxmatForContractListDto>> paged = ResultObject.createInstance(bizIAuxmatForContractListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 版本复制
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject versionCopy(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("版本复制成功"));
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getId());

        if (bizIAuxmatForContractHead == null) {
            throw new ErrorException(400, "辅料合同数据不存在，请刷新");
        }

        // 查询最大版本号
        BizIAuxmatForContractHead maxVersionNoData = bizIAuxmatForContractHeadMapper.getMaxVersionNoByContract(bizIAuxmatForContractHead);
        if (maxVersionNoData == null) {
            throw new ErrorException(400, XdoI18nUtil.t("查询不到有效数据，请刷新后再试！"));
        }
        Integer maxVersionNoInt = Integer.valueOf(maxVersionNoData.getVersionNo()) + 1;

        // 新表头id
        String sid = UUID.randomUUID().toString();

        // 获取表体数据并复制
        List<BizIAuxmatForContractList> oldListSids = bizIAuxmatForContractListMapper.getContractListByHeadId(bizIAuxmatForContractHeadParam.getId());
        for (BizIAuxmatForContractList old : oldListSids) {
            // 版本复制数据 在原sid上拼接版本号
            String newListId = old.getId().split("_")[0] + "_" + maxVersionNoInt;
            String oldListId = old.getId().split("_")[0];
            if (!"1".equals(maxVersionNoData.getVersionNo())) {
                oldListId = oldListId + "_" + maxVersionNoData.getVersionNo();
            }

            bizIAuxmatForContractListMapper.updateCorrelationID(newListId, oldListId, sid, maxVersionNoData.getId());
            old.setVersionNo(maxVersionNoInt.toString());
            old.setId(newListId);
            old.setHeadId(sid);
            old.setCreateBy(userInfo.getUserNo());
            old.setInsertUserName(userInfo.getUserName());
            old.setCreateTime(new Date());
            old.setUpdateBy(null);
            old.setUpdateUserName(null);
            old.setUpdateTime(null);
        }

        // 更新现在合同号数据为 2 作废
        bizIAuxmatForContractHeadMapper.updateCancelByContract(bizIAuxmatForContractHead.getContractNo(), bizIAuxmatForContractHead.getTradeCode());

        // 复制表头数据
        bizIAuxmatForContractHead.setId(sid);
        bizIAuxmatForContractHead.setCreateTime(new Date());
        bizIAuxmatForContractHead.setCreateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setInsertUserName(userInfo.getUserName());
        bizIAuxmatForContractHead.setDocStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizIAuxmatForContractHead.setConfirmDate(null);
        bizIAuxmatForContractHead.setAuditStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue());
        bizIAuxmatForContractHead.setVersionNo(maxVersionNoInt.toString());
        bizIAuxmatForContractHead.setUpdateBy(null);
        bizIAuxmatForContractHead.setUpdateTime(null);
        bizIAuxmatForContractHead.setUpdateUserName(null);
        bizIAuxmatForContractHeadMapper.insert(bizIAuxmatForContractHead);

        // 插入表体数据
        if (CollectionUtils.isNotEmpty(oldListSids)){
            oldListSids.stream().forEach(item -> {
                bizIAuxmatForContractListMapper.insert(item);
            });
        }

        // 复制随附单证文件
        List<Attached> attachedList = bizIOrderHeadMapper.getAttachmentFile(bizIAuxmatForContractHeadParam.getId());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(sid);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制辅料合同表头【"+bizIAuxmatForContractHead.getContractNo()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }

        return result;
    }

    public ResultObject checkStatus(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        Integer count = bizIAuxmatForContractHeadMapper.checkStatusByContractNo(bizIAuxmatForContractHeadParam.getId());
        result.setData(count);
        return result;
    }

    public ResultObject getSellerList(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        bizIAuxmatForContractHeadParam.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatForContractHead> bizIAuxmatForContractHeads = bizIAuxmatForContractHeadMapper.getSellerList(bizIAuxmatForContractHeadParam);
        List<KeyValuePair> keyValuePairList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bizIAuxmatForContractHeads)) {
            for (BizIAuxmatForContractHead map : bizIAuxmatForContractHeads) {
                KeyValuePair keyValuePair = new KeyValuePair(map.getSupplierName(), map.getCustomerName());
                if (!keyValuePairList.contains(keyValuePair)){
                    keyValuePairList.add(keyValuePair);
                }
            }
        }
        result.setData(keyValuePairList);
        return result;
    }

    public ResultObject getBuyerList(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        bizIAuxmatForContractHeadParam.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatForContractHead> bizIAuxmatForContractHeads = bizIAuxmatForContractHeadMapper.getBuyerList(bizIAuxmatForContractHeadParam);
        List<KeyValuePair> keyValuePairList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bizIAuxmatForContractHeads)) {
            for (BizIAuxmatForContractHead map : bizIAuxmatForContractHeads) {
                KeyValuePair keyValuePair = new KeyValuePair(map.getCustomerName(), map.getSupplierName());
                if (!keyValuePairList.contains(keyValuePair)){
                    keyValuePairList.add(keyValuePair);
                }
            }
        }
        result.setData(keyValuePairList);
        return result;
    }
}
