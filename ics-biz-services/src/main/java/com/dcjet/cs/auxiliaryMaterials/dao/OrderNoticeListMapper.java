package com.dcjet.cs.auxiliaryMaterials.dao;

import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;

public interface OrderNoticeListMapper extends Mapper<OrderNoticeList> {
    /**
     * 获取列表
     *
     * @param orderNoticeList 订货通知表体
     * @return 订货通知表体列表
     */
    List<OrderNoticeList> getList(OrderNoticeList orderNoticeList);

    /**
     * 获取总数量
     *
     * @param orderNoticeList 订货通知表体
     * @return 总数量
     */
    BigDecimal getTotalQty(OrderNoticeList orderNoticeList);

    /**
     * 根据关联键获取列表
     *
     * @param headIds 关联键列表
     * @return 订货通知表体列表
     */
    List<OrderNoticeList> getByHeadIds(@Param("headIds") List<String> headIds, @Param("tradeCode") String tradeCode);

    /**
     * 根据主键列表删除
     *
     * @param ids 主键列表
     * @return deleted rows count
     */
    int deleteByIds(@Param("ids") List<String> ids);

    /**
     * 根据表头主键列表删除
     *
     * @param headIds 表头主键列表
     * @return deleted rows count
     */
    int deleteByHeadIds(@Param("headIds") List<String> headIds);

    void updateCorrelationID(@Param("newListId") String newListId, @Param("oldSid") String oldSid);

    int deleteByHeadIds(@Param("headIds") List<String> headIds, @Param("tradeCode") String tradeCode);

    /**
     * 根据购销合同表体主键计数
     *
     * @param buyListIds 购销合同表体主键列表
     * @return 计数
     */
    int getCountByBuyListIds(@Param("buyListIds") List<String> buyListIds, @Param("tradeCode") String tradeCode);

    /**
     * 根据购销合同表体主键获取已超额数量的计数
     *
     * @param buyListIds 购销合同表体主键列表
     * @param tradeCode  企业编码
     * @return 计数
     */
    int getGeQtyCountByListIds(@Param("buyListIds") List<String> buyListIds, @Param("tradeCode") String tradeCode);

    /**
     * 根据关联键确认
     *
     * @param headId       关联键
     * @param updateBy     修改用户名
     * @param updateByName 修改用户名称
     */
    void confirmByHeadId(@Param("headId") String headId, @Param("updateBy") String updateBy
            , @Param("updateByName") String updateByName);

    /**
     * 根据关联键作废
     *
     * @param headId       关联键
     * @param updateBy     修改用户名
     * @param updateByName 修改用户名称
     */
    void invalidateByHeadId(@Param("headId") String headId, @Param("updateBy") String updateBy
            , @Param("updateByName") String updateByName);

    /**
     * 获取购销合同剩余未选择的数据
     *
     * @param contractIds 购销合同表头id列表
     * @param tradeCode   企业编码
     * @return 未选择的表体数据
     */
    List<OrderNoticeList> getRemainUnSelectedData(@Param("contractIds") List<String> contractIds, @Param("tradeCode") String tradeCode);

    /**
     * 根据订货编号作废
     *
     * @param orderNo   订货编号
     * @param tradeCode 企业编码
     */
    void invalidateByOrderNo(@Param("orderNo") String orderNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据关联键获取未选择的数据
     *
     * @param headIds   关联键列表
     * @param tradeCode 企业编码
     * @return 订货通知列表
     */
    List<OrderNoticeList> getByHeadIdsAndNotSelected(@Param("headIds") List<String> headIds, @Param("tradeCode") String tradeCode);
}