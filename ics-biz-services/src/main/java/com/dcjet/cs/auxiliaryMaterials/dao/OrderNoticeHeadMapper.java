package com.dcjet.cs.auxiliaryMaterials.dao;

import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeSelectBuyContractDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface OrderNoticeHeadMapper extends Mapper<OrderNoticeHead> {
    /**
     * 获取列表
     *
     * @param orderNoticeHead 订货通知表头
     * @return 订货通知表头列表
     */
    List<OrderNoticeHead> getList(OrderNoticeHead orderNoticeHead);

    /**
     * 获取所有客户
     *
     * @param orderNoticeHead 订货通知表头
     * @return 客户代码列表
     */
    List<String> getAllCustomers(OrderNoticeHead orderNoticeHead);

    /**
     * 根据主键列表获取数据
     *
     * @param ids 主键列表
     * @return 订货通知表头列表
     */
    List<OrderNoticeHead> getByIds(@Param("ids") List<String> ids);

    /**
     * 根据主键列表删除
     *
     * @param ids 主键列表
     * @return deleted rows count
     */
    int deleteByIds(@Param("ids") List<String> ids);

    /**
     * 获取选择购销合同列表
     *
     * @param tradeCode 企业编码
     * @return 购销合同选择列表
     */
    List<OrderNoticeSelectBuyContractDto> getSelectBuyContractList(@Param("tradeCode") String tradeCode);

    /**
     * 根据订货编号计数
     *
     * @param orderNo   订货编号
     * @param tradeCode 企业编码
     * @return 计数
     */
    Integer getCountByOrderNo(@Param("orderNo") String orderNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据订货编号获取订货通知
     *
     * @param orderNo   订货编号
     * @param tradeCode 企业编码
     * @return 订货通知表头列表
     */
    List<OrderNoticeHead> getByOrderNo(@Param("orderNo") String orderNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据主键确认
     *
     * @param id           主键
     * @param updateBy     修改用户名
     * @param updateByName 修改用户名称
     */
    void confirmById(@Param("id") String id, @Param("updateBy") String updateBy
            , @Param("updateByName") String updateByName);

    /**
     * 根据主键作废
     *
     * @param id           主键
     * @param updateBy     修改用户名
     * @param updateByName 修改用户名称
     */
    void invalidateById(@Param("id") String sid, @Param("updateBy") String updateBy
            , @Param("updateByName") String updateByName);

    /**
     * 获取最大版本号数据
     *
     * @param orderNo   订货编号
     * @param tradeCode 企业编码
     * @return 最大版本号订货通知
     */
    OrderNoticeHead getMaxVersionDataByOrderNo(@Param("orderNo") String orderNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据订货编号作废
     *
     * @param orderNo   订货编号
     * @param tradeCode 企业编码
     */
    void invalidateByOrderNo(@Param("orderNo") String orderNo, @Param("tradeCode") String tradeCode);
}