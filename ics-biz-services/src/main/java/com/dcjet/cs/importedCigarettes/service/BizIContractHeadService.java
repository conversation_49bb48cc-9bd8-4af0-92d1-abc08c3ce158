package com.dcjet.cs.importedCigarettes.service;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizIOrderListMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseHeadMapper;
import com.dcjet.cs.dto.bi.BizMerchantDto;
import com.dcjet.cs.dto.bi.BizMerchantParam;
import com.dcjet.cs.importedCigarettes.dao.BizIContractListMapper;
import com.dcjet.cs.importedCigarettes.model.BizIContractList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.importedCigarettes.mapper.BizIContractHeadDtoMapper;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.model.vo.StartFlowVoBatch;
import com.yuncheng.workflow.model.vo.WorkFlowLogVo;
import com.yuncheng.workflow.vo.HttpResult;
import jdk.nashorn.internal.runtime.options.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Slf4j
@Service
public class BizIContractHeadService extends BaseService<BizIContractHead> {
    @Resource
    private BizIContractListService bizIContractListService;
    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;
    @Resource
    private BizIContractListMapper bizIContractListMapper;
    @Resource
    private BizIContractHeadDtoMapper bizIContractHeadDtoMapper;
    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;
    @Resource
    private BizIOrderListMapper bizIOrderListMapper;
    @Resource
    private BizIPurchaseHeadMapper bizIPurchaseHeadMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private AttachedService attachedService;
    @Resource
    private CommonService commonService;
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;

    @Override
    public Mapper<BizIContractHead> getMapper() {
        return bizIContractHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIContractHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIContractHeadDto>> getListPaged(BizIContractHeadParam bizIContractHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizIContractHead bizIContractHead = bizIContractHeadDtoMapper.toPo(bizIContractHeadParam);
        Page<BizIContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIContractHeadMapper.getList(bizIContractHead));
        List<BizIContractHeadDto> bizIContractHeadDtos = page.getResult().stream().map(head -> {
            BizIContractHeadDto dto = bizIContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIContractHeadDto>> paged = ResultObject.createInstance(bizIContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public ResultObject<List<BizIContractHeadDto>> getPlanListPaged(BizIContractHeadParam bizIContractHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizIContractHead bizIContractHead = bizIContractHeadDtoMapper.toPo(bizIContractHeadParam);
        Page<BizIContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIContractHeadMapper.getPlanListPaged(bizIContractHead));
        List<BizIContractHeadDto> bizIContractHeadDtos = page.getResult().stream().map(head -> {
            BizIContractHeadDto dto = bizIContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizIContractHeadDto>> paged = ResultObject.createInstance(bizIContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIContractHeadDto insert(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        BizIContractHead bizIContractHead = bizIContractHeadDtoMapper.toPo(bizIContractHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIContractHead.setSid(sid);
        bizIContractHead.setInsertUser(userInfo.getUserNo());
        bizIContractHead.setInsertTime(new Date());
        bizIContractHead.setTradeCode(userInfo.getCompany());

        //表体从进口计划表体带值
        bizIContractListMapper.insertByPlanList(bizIContractHead.getPlanNo(),bizIContractHead.getSeller(), bizIContractHead.getTradeCode(), sid, userInfo.getUserNo());

        bizIContractHead.setBusinessType(CommonEnum.businessTypeEnum.type_1.getCode());
        //获取中国烟草国际有限公司 code
        String buyerCode = bizIContractHeadMapper.getBuyerCodeByName("中国烟草国际有限公司", userInfo.getCompany());
        if (StringUtils.isNotBlank(buyerCode)) {
            bizIContractHead.setBuyer(buyerCode);
        }else {
            bizIContractHead.setBuyer("中国烟草国际有限公司");
        }
        bizIContractHead.setPriceTermPort("1");
        bizIContractHead.setPrepareTime(new Date());
        bizIContractHead.setPreparedBy(userInfo.getUserName());
        bizIContractHead.setDataStatus(Constants.JOB_STATUS_0);
        bizIContractHead.setApprovalStatus(Constants.JOB_STATUS_1);
        bizIContractHead.setVersionNo("1");
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        bizIContractHead.setContractEffectiveDate(sdf.format(date));
        bizIContractHead.setTradeTerms("CIF");
        bizIContractHead.setPriceTermPort("上海");
        // 新增数据
        int insertStatus = bizIContractHeadMapper.insert(bizIContractHead);
        //按计划号+商品名称，汇总已存在本模块中的合同数量（作废的不参与计算）
        bizIContractListMapper.updateListContractQty(bizIContractHead.getSid(), bizIContractHead.getPlanNo());
        //删除合同数量为0的
        bizIContractListMapper.deleteContractQtyIsZero(sid);
//        //更新表头总数
//        bizIContractHeadMapper.updateContractAmountAndQty(sid);
        bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(sid);
        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizIContractHead.getSid());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "0", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        return  insertStatus > 0 ? bizIContractHeadDtoMapper.toDto(bizIContractHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIContractHeadDto update(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractHeadParam.getSid());
        bizIContractHeadDtoMapper.updatePo(bizIContractHeadParam, bizIContractHead);
        bizIContractHead.setUpdateUser(userInfo.getUserNo());
        bizIContractHead.setUpdateTime(new Date());
        bizIContractHead.setPreparedBy(userInfo.getUserName());
        bizIContractHead.setPrepareTime(new Date());
        //合同号唯一性校验
        if (bizIContractHeadMapper.checkContractNoExits(bizIContractHead) > 0){
            throw new ErrorException(400, "合同号已存在，请重新输入！");
        }
//        //获取表体汇总总金额及总数量
//        BizIContractHead amountAndQty = bizIContractHeadMapper.getAllListAmountAndQty(bizIContractHead.getSid());
//        bizIContractHead.setTotalAmount(amountAndQty.getTotalAmount());
//        bizIContractHead.setTotalQuantity(amountAndQty.getTotalQuantity());
        // 更新数据
        int update = bizIContractHeadMapper.updateByPrimaryKey(bizIContractHead);
        // 更新已形成合同数量，添加死锁异常捕获
        bizIContractListService.updateListFormedQuantity(bizIContractHead.getPlanNo());
        return update > 0 ? bizIContractHeadDtoMapper.toDto(bizIContractHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        if (bizIContractHeadMapper.checkCanDelBySids(sids) > 0){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }
        if (bizIContractHeadMapper.checkContractIsUsedBySids(sids) > 0){
            throw new ErrorException(400, "该单据在关联模块已产生数据，不允许删除");
        }
        List<String> planNoList = new ArrayList<>(sids.size());
        for (String sid : sids) {
            BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(sid);
            if (!planNoList.contains(bizIContractHead.getPlanNo())){
                planNoList.add(bizIContractHead.getPlanNo());
            }
        }
		bizIContractHeadMapper.deleteBySids(sids);
        for (String planNo : planNoList) {
            // 更新已形成合同数量
            bizIContractListMapper.updateListFormedQuantity(planNo);
        }
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIContractHeadDto> selectAll(BizIContractHeadParam exportParam, UserInfoToken userInfo) {
        BizIContractHead bizIContractHead = bizIContractHeadDtoMapper.toPo(exportParam);
        bizIContractHead.setTradeCode(userInfo.getCompany());
        List<BizIContractHeadDto> bizIContractHeadDtos = new ArrayList<>();
        List<BizIContractHead> bizIContractHeads = bizIContractHeadMapper.getList(bizIContractHead);
        if (CollectionUtils.isNotEmpty(bizIContractHeads)) {
            bizIContractHeadDtos = bizIContractHeads.stream().map(head -> {
                BizIContractHeadDto dto = bizIContractHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIContractHeadDtos;
    }

    public ResultObject getParamsSelectByType(BaseInfoCustomerParams params, String businessType, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        List<BaseInfoCustomerParams> listResult = baseInfoCustomerParamsMapper.getParamsSelectByType(params.getParamsType(), businessType, userInfo.getCompany());
        List<KeyValuePair> keyValuePairList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(listResult)) {
            for (BaseInfoCustomerParams map : listResult) {
                if ("COUNTRY".equals(params.getParamsType())){
                    keyValuePairList.add(new KeyValuePair(map.getParamsName(), map.getCustomParamName()));
                } else if ("CURR".equals(params.getParamsType())) {
                    keyValuePairList.add(new KeyValuePair(map.getCustomParamCode(), map.getParamsName()));
                } else {
                    keyValuePairList.add(new KeyValuePair(map.getParamsCode(), map.getParamsName()));
                }
            }
        }
        result.setData(keyValuePairList);
        return result;
    }

    public ResultObject confirmDataStatus(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));
        BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractHeadParam.getSid());
        if (bizIContractHead == null) {
            throw new ErrorException(400, "进口合同数据不存在，请刷新");
        }
        if ("1".equals(bizIContractHead.getDataStatus())){
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        bizIContractHead.setDataStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizIContractHead.setUpdateUser(userInfo.getUserNo());
        bizIContractHead.setUpdateTime(new Date());
        bizIContractHead.setUpdateUserName(userInfo.getUserName());
        bizIContractHead.setPreparedBy(userInfo.getUserName());
        bizIContractHead.setPrepareTime(new Date());
        bizIContractHead.setConfirmTime(new Date());
        bizIContractHeadMapper.updateByPrimaryKey(bizIContractHead);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendAudit(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) throws InterruptedException {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审核成功"));
        BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractHeadParam.getSid());
        if (bizIContractHead == null) {
            throw new ErrorException(400, "进口合同数据不存在，请刷新");
        }
        if (!"1".equals(bizIContractHead.getDataStatus())){
            throw new ErrorException(400, "只有数据状态为1确认的数据允许操作发送审批");
        }
        if (!CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue().equals(bizIContractHead.getApprovalStatus())
            && !CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue().equals(bizIContractHead.getApprovalStatus())){
            throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
        }
        // 调用发送审核 审批流
        // 解析返回信息
        NextNodeInfoBatchVo batchVo = commonService.startFlowBatch(bizIContractHeadParam.getBusinessType(), bizIContractHeadParam.getBillType(), bizIContractHeadParam.getIds(), userInfo);
        //记录flowInstanceId
        bizIContractHead.setExtend2(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));

        //表头更新状态
        bizIContractHead.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        bizIContractHead.setUpdateUser(userInfo.getUserNo());
        bizIContractHead.setUpdateTime(new Date());
        bizIContractHead.setUpdateUserName(userInfo.getUserName());
        bizIContractHead.setPreparedBy(userInfo.getUserName());
        bizIContractHead.setPrepareTime(new Date());
        bizIContractHeadMapper.updateByPrimaryKey(bizIContractHead);

        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizIContractHeadParam.getSid());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        return result;
    }

    public ResultObject cancelDataStatus(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));
        BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractHeadParam.getSid());
        if (bizIContractHead == null) {
            throw new ErrorException(400, "进口合同数据不存在，请刷新");
        }
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizIContractHead.getApprovalStatus())){
            throw new ErrorException(400, "审批中的数据不允许作废");
        }
        //校验合同号是否在下游数据使用
        if (bizIContractHeadMapper.checkContractIsUsed(bizIContractHead.getContractNo()) > 0){
            throw new ErrorException(400, "进口管理订单数据存在此合同号,不允许作废");
        }
        bizIContractHead.setDataStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizIContractHead.setUpdateUser(userInfo.getUserNo());
        bizIContractHead.setUpdateTime(new Date());
        bizIContractHead.setUpdateUserName(userInfo.getUserName());
        bizIContractHead.setPreparedBy(userInfo.getUserName());
        bizIContractHead.setPrepareTime(new Date());
        bizIContractHeadMapper.updateByPrimaryKey(bizIContractHead);
        // 更新已形成合同数量
        bizIContractListMapper.updateListFormedQuantity(bizIContractHead.getPlanNo());
        return result;
    }

    public ResultObject checkStatus(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        Integer count = bizIContractHeadMapper.checkStatusByContractNo(bizIContractHeadParam.getSid());
        result.setData(count);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject versionCopy(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("版本复制成功"));
        BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractHeadParam.getSid());

        //查询最大版本号
        BizIContractHead maxVersionNoData = bizIContractHeadMapper.getMaxVersionNoByContract(bizIContractHead);
        if (maxVersionNoData == null) {
            throw new ErrorException(400, XdoI18nUtil.t("查询不到有效数据，请刷新后再试！"));
        }
        Integer maxVersionNoInt = Integer.valueOf(maxVersionNoData.getVersionNo()) + 1;

        //新表头id
        String sid = UUID.randomUUID().toString();

        //使用最大版本号找下游数据 更新下游数据关联id
        List<BizIContractList> oldListSids = bizIContractListMapper.getContractListByHeadId(bizIContractHeadParam.getSid());
        for (BizIContractList old : oldListSids) {
            //版本复制数据 再原sid上拼接版本号

            String newListId = old.getSid().split("_")[0] + "_" + maxVersionNoInt;
            String oldListId = old.getSid().split("_")[0];
            if (!"1".equals(maxVersionNoData.getVersionNo())) {
                oldListId = oldListId + "_" + maxVersionNoData.getVersionNo();
            }

            bizIOrderListMapper.updateCorrelationID(newListId, oldListId, sid, maxVersionNoData.getSid());
            old.setVersionNo(maxVersionNoInt.toString());
            old.setSid(newListId);
            old.setHeadId(sid);
            old.setInsertUser(userInfo.getUserNo());
            old.setInsertUserName(userInfo.getUserName());
            old.setInsertTime(new Date());
            old.setUpdateUser(null);
            old.setUpdateUserName(null);
            old.setUpdateTime(null);
        }

        //更新现在合同号数据为 2 作废
        bizIContractHeadMapper.updateCancelByContract(bizIContractHead.getContractNo(), bizIContractHead.getTradeCode());

        //bizIContractListMapper.copyListByHeadId(bizIContractHead.getSid(), sid, bizIContractHead.getTradeCode());

        bizIContractHead.setSid(sid);
        bizIContractHead.setInsertTime(new Date());
        bizIContractHead.setInsertUser(userInfo.getUserNo());
        bizIContractHead.setInsertUserName(userInfo.getUserName());
        bizIContractHead.setPreparedBy(userInfo.getUserName());
        bizIContractHead.setPrepareTime(new Date());
        bizIContractHead.setDataStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizIContractHead.setConfirmTime(null);
        bizIContractHead.setApprovalStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        bizIContractHead.setVersionNo(maxVersionNoInt.toString());
        bizIContractHeadMapper.insert(bizIContractHead);

        if (CollectionUtils.isNotEmpty(oldListSids)){
            oldListSids.stream().forEach(item -> {
                bizIContractListMapper.insert(item);
            });
        }

        // 复制随附单证文件
        List<Attached> attachedList = bizIOrderHeadMapper.getAttachmentFile(bizIContractHeadParam.getSid());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(sid);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制合同表头【"+bizIContractHead.getContractNo()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }

        return result;
    }

    public ResultObject getSellerList(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        BizIContractHead bizIContractHead = bizIContractHeadDtoMapper.toPo(bizIContractHeadParam);
        bizIContractHead.setTradeCode(userInfo.getCompany());
        List<BizIContractHead> bizIContractHeads = bizIContractHeadMapper.getSellerList(bizIContractHead);

        List<KeyValuePair> keyValuePairList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bizIContractHeads)) {
            for (BizIContractHead map : bizIContractHeads) {
                 KeyValuePair keyValuePair = new KeyValuePair(map.getSeller(), map.getBuyer());
                 if (!keyValuePairList.contains(keyValuePair)){
                    keyValuePairList.add(keyValuePair);
                }
            }
        }
        result.setData(keyValuePairList);
        return result;
    }

    /**
     * 审核
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject audit(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) throws InterruptedException {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核成功"));

        List<String> sids = bizIContractHeadParam.getIds();
        if (CollectionUtils.isEmpty(sids)) {
            throw new ErrorException(400, "请选择要审核的数据！");
        }
        //校验审核状态
        if (bizIContractHeadMapper.checkApprovalStatus(sids, CommonEnum.OrderApprStatusEnum.APPROVING.getValue()) > 0){
            throw new ErrorException(400, "存在不是<审批中>状态的数据，不允许操作！");
        }
        List<WorkFlowParam> flows = bizIContractHeadMapper.selectBySids(sids);
        Map<String, String> flowInstanceMap = flows.stream().collect(Collectors.toMap(WorkFlowParam::getFlowInstanceId, WorkFlowParam::getSid));
        // 调用 审批通过-审批流
        // 解析 返回结果
        List<NextNodeInfoVo> nextNodeInfoVos = commonService.passBatch(flows, bizIContractHeadParam.getApprMessage(), userInfo);

        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId()));
            if (nextNodeInfoVo.isFinish()) {
                bizIContractHead.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                bizIContractHead.setUpdateUser(userInfo.getUserNo());
                bizIContractHead.setUpdateTime(new Date());
                bizIContractHead.setUpdateUserName(userInfo.getUserName());
                bizIContractHeadMapper.updateByPrimaryKey(bizIContractHead);
                nextNodeInfoVo.setNodeName("审核通过");
            }
            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizIContractHead.getSid());
            aeoAuditInfo.setApprNote(bizIContractHeadParam.getApprMessage());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName(), userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }

        return result;
    }

    /**
     * 审核退回
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    public ResultObject reject(BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核退回成功"));

        List<WorkFlowParam> flows = bizIContractHeadMapper.selectBySids(bizIContractHeadParam.getIds());
        // 审核退回 对接审批流
        // 解析 返回结果
        List<NextNodeInfoVo> nextNodeInfoVos = commonService.rejectBatch(flows, bizIContractHeadParam.getApprMessage(), userInfo);

        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            //回退到发起人
            BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractHeadParam.getIds().get(0));

            bizIContractHead.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
            bizIContractHead.setUpdateUser(userInfo.getUserNo());
            bizIContractHead.setUpdateTime(new Date());
            bizIContractHead.setUpdateUserName(userInfo.getUserName());
            bizIContractHeadMapper.updateByPrimaryKey(bizIContractHead);

            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizIContractHead.getSid());
            aeoAuditInfo.setApprNote(bizIContractHeadParam.getApprMessage());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
        return result;
    }

    /**
     * 待审核页面查询
     * @param bizIContractHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    public ResultObject<List<BizIContractHeadDto>> getAeoListPaged(BizIContractHeadParam bizIContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalList(bizIContractHeadParam.getBusinessType());
        List<String> ids = (List<String>) result.getResult();

        // 如果ids为空，直接返回空分页结果
        if (ids == null || ids.isEmpty()) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }

        bizIContractHeadParam.setIds(ids);

        // 启用分页查询
        BizIContractHead bizIContractHead = bizIContractHeadDtoMapper.toPo(bizIContractHeadParam);
        Page<BizIContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIContractHeadMapper.getAeoList(bizIContractHead));
        List<BizIContractHeadDto> bizIContractHeadDtos = page.getResult().stream()
                .map(bizIContractHeadDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(bizIContractHeadDtos, (int) page.getTotal(), page.getPageNum());
    }
}
