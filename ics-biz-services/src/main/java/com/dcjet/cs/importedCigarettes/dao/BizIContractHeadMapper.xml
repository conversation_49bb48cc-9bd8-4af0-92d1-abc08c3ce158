<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper">
    <resultMap id="bizIContractHeadResultMap" type="com.dcjet.cs.importedCigarettes.model.BizIContractHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="plan_no" property="planNo" jdbcType="VARCHAR" />
		<result column="plan_year" property="planYear" jdbcType="TIMESTAMP" />
		<result column="half_year" property="halfYear" jdbcType="VARCHAR" />
		<result column="buyer" property="buyer" jdbcType="VARCHAR" />
		<result column="seller" property="seller" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="contract_effective_date" property="contractEffectiveDate" jdbcType="VARCHAR" />
		<result column="contract_expiry_date" property="contractExpiryDate" jdbcType="TIMESTAMP" />
		<result column="sign_date" property="signDate" jdbcType="TIMESTAMP" />
		<result column="loading_port" property="loadingPort" jdbcType="VARCHAR" />
		<result column="arrival_port" property="arrivalPort" jdbcType="VARCHAR" />
		<result column="trade_terms" property="tradeTerms" jdbcType="VARCHAR" />
		<result column="price_term_port" property="priceTermPort" jdbcType="VARCHAR" />
		<result column="export_country" property="exportCountry" jdbcType="VARCHAR" />
		<result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
		<result column="total_quantity" property="totalQuantity" jdbcType="NUMERIC" />
		<result column="short_over_percent" property="shortOverPercent" jdbcType="NUMERIC" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="prepared_by" property="preparedBy" jdbcType="VARCHAR" />
		<result column="prepare_time" property="prepareTime" jdbcType="TIMESTAMP" />
		<result column="data_status" property="dataStatus" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="approval_status" property="approvalStatus" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="plan_quantity" property="planQuantity" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,business_type
     ,plan_no
     ,plan_year
     ,half_year
     ,buyer
     ,seller
     ,contract_no
     ,contract_effective_date
     ,contract_expiry_date
     ,sign_date
     ,loading_port
     ,arrival_port
     ,trade_terms
     ,price_term_port
     ,export_country
     ,(SELECT
               COALESCE(SUM(l.total_value), 0) as total_amount
      FROM t_biz_i_contract_list l
      WHERE l.HEAD_ID = t.sid ) as total_amount
     ,(SELECT
            COALESCE(SUM(l.contract_quantity),0) as total_quantity
        FROM t_biz_i_contract_list l
        WHERE l.HEAD_ID = t.sid ) as total_quantity
     ,short_over_percent
     ,note
     ,prepared_by
     ,prepare_time
     ,data_status
     ,confirm_time
     ,approval_status
     ,version_no
     ,trade_code
     ,parent_id
     ,insert_user
     ,insert_time
     ,update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
    </sql>
    <sql id="condition">
    <if test="planNo != null and planNo != ''">
	  and plan_no like '%'|| #{planNo} || '%'
	</if>
    <if test="seller != null and seller != ''">
		and seller = #{seller}
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
        <if test="_databaseId != 'postgresql' and prepareTimeFrom != null and prepareTimeFrom != ''">
            <![CDATA[ and prepare_time >= to_date(#{prepareTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId != 'postgresql' and prepareTimeTo != null and prepareTimeTo != ''">
            <![CDATA[ and prepare_time <= to_date(#{prepareTimeTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and prepareTimeFrom != null and prepareTimeFrom != ''">
            <![CDATA[ and prepare_time >= to_timestamp(#{prepareTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and prepareTimeTo != null and prepareTimeTo != ''">
            <![CDATA[ and prepare_time <= to_timestamp(#{prepareTimeTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
        <if test="dataStatus != null and dataStatus != ''">
            and data_status = #{dataStatus}
        </if>
        <if test="dataStatus == null or dataStatus == ''">
            and data_status in ('0', '1')
        </if>
        <if test="approvalStatus != null and approvalStatus != ''">
            and approval_status = #{approvalStatus}
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and trade_code = #{tradeCode}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIContractHeadResultMap" parameterType="com.dcjet.cs.importedCigarettes.model.BizIContractHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_contract_head t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.insert_time desc
    </select>
    <select id="getPlanListPaged" resultMap="bizIContractHeadResultMap" parameterType="com.dcjet.cs.importedCigarettes.model.BizIContractHead">
        WITH PlanSummary AS (
            SELECT
                p.PLAN_ID,
                MAX(p.PLAN_YEAR) AS PLAN_YEAR,  -- 添加聚合字段
                MAX(p.HALF_YEAR) AS HALF_YEAR,  -- 添加聚合字段
                l.SUPPLIER,
                COALESCE(SUM(l.PLAN_QUANTITY), 0) AS total_plan,
                COALESCE(
                    (SELECT SUM(sub.total)
                    FROM (
                        SELECT COALESCE(SUM(DISTINCT cl.FORMED_QUANTITY), 0) AS total
                        FROM T_BIZ_I_CONTRACT_HEAD ch
                        LEFT JOIN T_BIZ_I_CONTRACT_LIST cl ON ch.SID = cl.HEAD_ID
                        WHERE ch.PLAN_NO = p.PLAN_ID
                        AND ch.SELLER = l.SUPPLIER
                        AND ch.DATA_STATUS != '2'
                        AND ch.trade_code = #{tradeCode}
                        GROUP BY cl.GOODS_BRAND
                        ) sub),
                    0) AS total_formed
        FROM T_BIZ_I_PLAN p
            LEFT JOIN T_BIZ_I_PLAN_LIST l ON l.HEAD_ID = p.SID
        WHERE p.STATUS = '1' and p.trade_code = #{tradeCode}
        GROUP BY p.PLAN_ID, l.SUPPLIER  -- 确保分组字段包含所有非聚合列
            )
        SELECT
            PLAN_ID AS PLAN_NO,
            PLAN_YEAR,        -- 直接使用CTE中的字段
            HALF_YEAR,        -- 直接使用CTE中的字段
            SUPPLIER AS seller,
            (total_plan - total_formed) AS PLAN_QUANTITY
        FROM PlanSummary
        WHERE (total_plan - total_formed) > 0
            <if test="planNo != null and planNo != ''">
                and PLAN_ID like '%'|| #{planNo} || '%'
            </if>
        ORDER BY PLAN_ID DESC, SUPPLIER DESC;
    </select>
    <select id="checkContractNoExits" resultType="java.lang.Integer">
        select count(1) from t_biz_i_contract_head t
        where
            t.contract_no = #{contractNo} and t.data_status != '2'
        <if test="sid != null and sid != ''">
            and t.sid != #{sid}
        </if>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_contract_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        ;
        delete from t_biz_i_contract_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <update id="updateContractAmountAndQty">
        <![CDATA[
        UPDATE t_biz_i_contract_head h
        SET
            total_amount = COALESCE(
                    (SELECT SUM(l.total_value)
                     FROM t_biz_i_contract_list l
                     WHERE l.HEAD_ID = h.SID),
                    0
                           ),
            total_quantity =
                CASE WHEN COALESCE(
                        (SELECT SUM(l.contract_quantity)
                         FROM t_biz_i_contract_list l
                         WHERE l.HEAD_ID = h.SID),
                        0
                          ) <= 999999999
                then
                COALESCE(
                    (SELECT SUM(l.contract_quantity)
                     FROM t_biz_i_contract_list l
                     WHERE l.HEAD_ID = h.SID),
                    0
                             )
                else null end
        where h.sid = #{sid}
        ]]>
    </update>
    <select id="getAllListAmountAndQty" resultType="com.dcjet.cs.importedCigarettes.model.BizIContractHead">
        SELECT
            COALESCE(SUM(l.total_value), 0) as total_amount,
            COALESCE(SUM(l.contract_quantity),0) as total_quantity
        FROM t_biz_i_contract_list l
        WHERE l.HEAD_ID = #{sid}
    </select>
    <select id="checkContractIsUsed" resultType="java.lang.Integer">
        select count(1) from  T_BIZ_I_ORDER_LIST l
            left join T_BIZ_I_ORDER_HEAD h on l.head_id = h.sid
        where h.data_status != '2'
            and l.contract_no = #{contractNo}
    </select>
    <select id="checkStatusByContractNo" resultType="java.lang.Integer">
        select count(1) from t_biz_i_contract_head
        where contract_no = (
            select contract_no from t_biz_i_contract_head where sid = #{sid}
            )
        and data_status != '2'
    </select>
    <update id="updateCancelByContract">
        UPDATE t_biz_i_contract_head h
        SET data_status = '2'
        where contract_no = #{contractNo}
        and trade_code = #{tradeCode}
    </update>
    <select id="checkCanDelBySids" resultType="java.lang.Integer">
        select count(1)
        from t_biz_i_contract_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and t.data_status != '0'
    </select>
    <select id="checkContractIsUsedBySids" resultType="java.lang.Integer">
        select count(1) from  T_BIZ_I_ORDER_LIST l
                left join T_BIZ_I_ORDER_HEAD h on l.head_id = h.sid
        where h.data_status != '2'
            and l.contract_no in (
            select contract_no from t_biz_i_contract_head t
                where t.SID in
                <foreach collection="list"  item="item" open="(" separator="," close=")"  >
                    #{item}
                </foreach>
            )
    </select>
    <select id="getMaxVersionNoByContract" resultType="com.dcjet.cs.importedCigarettes.model.BizIContractHead">
        select sid,VERSION_NO  from T_BIZ_I_CONTRACT_HEAD
        where contract_no = #{contractNo}
          and trade_code = #{tradeCode}
        order by VERSION_NO desc limit 1
    </select>
    <select id="getSellerList" resultType="com.dcjet.cs.importedCigarettes.model.BizIContractHead">
        SELECT
            t.seller,
            m.MERCHANT_NAME_CN as buyer
        FROM
        t_biz_i_contract_head t
        left join T_BIZ_MERCHANT m on t.SELLER = m.MERCHANT_CODE and t.TRADE_CODE = m.TRADE_CODE
        <where>
            <if test="tradeCode != null and tradeCode != ''">
                and t.trade_code = #{tradeCode}
            </if>
        </where>
        order by t.insert_time desc
    </select>
    <select id="getBuyerCodeByName" resultType="java.lang.String">
        select MERCHANT_CODE from T_BIZ_MERCHANT where MERCHANT_NAME_CN = #{name} and TRADE_CODE = #{tradeCode}
    </select>
    <select id="selectBySids" resultType="com.dcjet.cs.common.model.WorkFlowParam">
        select
            sid,
            extend2 as flowInstanceId
        from T_BIZ_I_CONTRACT_HEAD where sid in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <select id="getAeoList" resultType="com.dcjet.cs.importedCigarettes.model.BizIContractHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_contract_head t
        <where>
            <include refid="condition"></include>
            and t.sid in
            <foreach collection="ids"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        </where>
        order by t.insert_time desc
    </select>
    <select id="checkApprovalStatus" resultType="java.lang.Integer">
        select count(1) from t_biz_i_contract_head
        where sid in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and approval_status != #{status}
    </select>

</mapper>
