<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.aeo.dao.AeoAuditInfoMapper">
    <resultMap id="aeoAuditInfoResultMap" type="com.dcjet.cs.aeo.model.AeoAuditInfo">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="APPR_TYPE" property="apprType" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="APPR_USER" property="apprUser" jdbcType="VARCHAR"/>
        <result column="APPR_DATE" property="apprDate" jdbcType="VARCHAR"/>
        <result column="APPR_NOTE" property="apprNote" jdbcType="VARCHAR"/>
        <result column="BILL_SID" property="businessSid" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="APPR_TYPE_NAME" jdbcType="VARCHAR" property="apprTypeName"/>
        <result column="STATUS_NAME" jdbcType="VARCHAR" property="statusName"/>
    </resultMap>
    <sql id="Base_Column_List">
        t.SID,
        t.APPR_TYPE,
        t.STATUS,
        t.APPR_USER,
        t.APPR_DATE,
        t.APPR_NOTE,
        t.BILL_SID,
        t.INSERT_USER,
        t.INSERT_TIME,
        t.UPDATE_USER,
        t.UPDATE_TIME,
        t.TRADE_CODE,
        t.INSERT_USER_NAME,
        t.UPDATE_USER_NAME,
        CASE T.STATUS WHEN '-1' THEN '审核退回' WHEN '-2' THEN '待审核' WHEN '2' THEN '初审通过' WHEN '8' THEN '终审通过' WHEN '4' THEN '接单' WHEN '3' THEN '变更' WHEN 'C' THEN '清单撤单' WHEN '0' THEN '制单' ELSE T.STATUS END AS STATUS_NAME,
        b.PARAMS_NAME AS APPR_TYPE_NAME
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and t.SID = #{sid}
        </if>
        <if test="apprType != null and apprType != ''">
            and t.APPR_TYPE = #{apprType}
        </if>
        <if test="status != null and status != ''">
            and t.STATUS = #{status}
        </if>
        <if test="apprUser != null and apprUser != ''">
            and t.APPR_USER = #{apprUser}
        </if>
        <if test="apprDateFrom != null and apprDateFrom != ''">
            <![CDATA[ and t.APPR_DATE >= to_date(#{apprDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="apprDateTo != null and apprDateTo != ''">
            <![CDATA[ and t.APPR_DATE < to_date(#{apprDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="apprNote != null and apprNote != ''">
            and t.APPR_NOTE = #{apprNote}
        </if>
        and t.BILL_SID = #{businessSid,jdbcType=VARCHAR}
        <if test="insertUser != null and insertUser != ''">
            and t.INSERT_USER = #{insertUser}
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.INSERT_TIME >= to_date(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.INSERT_TIME < to_date(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
        <if test="updateUser != null and updateUser != ''">
            and t.UPDATE_USER = #{updateUser}
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.UPDATE_TIME >= to_date(#{updateTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.UPDATE_TIME < to_date(#{updateTimeTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
        </if>
    </sql>
    <sql id="conditionPG">
        <if test="sid != null and sid != ''">
            and t.SID = #{sid}
        </if>
        <if test="apprType != null and apprType != ''">
            and t.APPR_TYPE = #{apprType}
        </if>
        <if test="status != null and status != ''">
            and t.STATUS = #{status}
        </if>
        <if test="apprUser != null and apprUser != ''">
            and t.APPR_USER = #{apprUser}
        </if>
        <if test="apprDateFrom != null and apprDateFrom != ''">
            <![CDATA[ and t.APPR_DATE >= to_timestamp(#{apprDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="apprDateTo != null and apprDateTo != ''">
            <![CDATA[ and t.APPR_DATE < to_timestamp(#{apprDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="apprNote != null and apprNote != ''">
            and t.APPR_NOTE = #{apprNote}
        </if>
        and t.BILL_SID = #{businessSid}
        <if test="insertUser != null and insertUser != ''">
            and t.INSERT_USER = #{insertUser}
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.INSERT_TIME >= to_timestamp(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.INSERT_TIME < to_timestamp(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="updateUser != null and updateUser != ''">
            and t.UPDATE_USER = #{updateUser}
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.UPDATE_TIME >= to_timestamp(#{updateTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.UPDATE_TIME < to_timestamp(#{updateTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="aeoAuditInfoResultMap" parameterType="com.dcjet.cs.aeo.model.AeoAuditInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_AEO_AUDIT_INFO T
        LEFT JOIN T_BI_CUSTOMER_PARAMS b ON t.APPR_TYPE = b.PARAMS_CODE
        AND b.PARAMS_TYPE = 'SEND_TYPE'
        <where>
            <if test='_databaseId == "postgresql" '>
                <include refid="conditionPG"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="condition"></include>
            </if>
        </where>
        ORDER BY t.APPR_DATE,t.STATUS
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_AEO_AUDIT_INFO t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


</mapper>
