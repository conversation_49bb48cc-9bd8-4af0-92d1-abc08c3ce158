<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BizMerchantMapper">
    <resultMap id="bizMerchantResultMap" type="com.dcjet.cs.bi.model.BizMerchant">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="data_status" property="dataStatus" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="merchant_code" property="merchantCode" jdbcType="VARCHAR" />
		<result column="merchant_name_cn" property="merchantNameCn" jdbcType="VARCHAR" />
		<result column="merchant_name_en" property="merchantNameEn" jdbcType="VARCHAR" />
		<result column="merchant_short" property="merchantShort" jdbcType="VARCHAR" />
		<result column="merchant_address" property="merchantAddress" jdbcType="VARCHAR" />
		<result column="receiving_bank" property="receivingBank" jdbcType="VARCHAR" />
		<result column="receiver_account_num" property="receiverAccountNum" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
        <result column="finance_code" property="financeCode" jdbcType="VARCHAR" />
        <result column="serial_no" property="serialNo" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
      id as sid
     ,business_type
     ,validate_status as data_status
     ,version_no
     ,trade_code
     ,parent_id
     ,create_by as insert_user
     ,create_time as insert_time
     ,update_by as update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,merchant_code
     ,merchant_name_cn
     ,merchant_name_en
     ,merchant_short
     ,merchant_address
     ,receiving_bank
     ,receiver_account_num
     ,note
     ,finance_code
     ,serial_no
    </sql>
    <sql id="condition">
        <if test="merchantCode != null and merchantCode != ''"> and merchant_code like '%'|| #{merchantCode} || '%' </if>
        <if test="merchantNameCn != null and merchantNameCn != ''"> and merchant_name_cn like '%'|| #{merchantNameCn} || '%' </if>
        <if test="merchantNameEn != null and merchantNameEn != ''"> and merchant_name_en like '%'|| #{merchantNameEn} || '%' </if>
        <if test="merchantShort != null and merchantShort != ''"> and merchant_short like '%'|| #{merchantShort} || '%' </if>
        <if test="financeCode != null and financeCode != ''"> and finance_code like '%'|| #{financeCode} || '%' </if>
         and trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizMerchantResultMap" parameterType="com.dcjet.cs.bi.model.BizMerchant">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_merchant t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.create_time desc
    </select>
    <select id="checkKey" resultType="java.lang.Integer">
        select count(1) from t_biz_merchant where MERCHANT_CODE = #{merchantCode} and TRADE_CODE = #{tradeCode}
        <if test="sid != null and sid != ''"> and id !=#{sid} </if>
    </select>
    <select id="selectPaclInfo" resultType="com.dcjet.cs.dto.bi.BizMaterialInformationKeyCode">
        select PACK_UNIT_CN_NAME as value,PARAM_CODE as label from T_BIZ_PACKAGE_INFO where  TRADE_CODE = #{company}
    </select>
    <select id="selectMerchandiseCategories" resultType="com.dcjet.cs.dto.bi.BizMaterialInformationKeyCode">
        select CATEGORY_NAME as value,CATEGORY_CODE as label from T_BIZ_PRODUCT_TYPE where  TRADE_CODE = #{company}
    </select>
    <select id="checkMerchantNameCn" resultType="java.lang.Integer">
        select count(1) from t_biz_merchant where merchant_name_cn = #{merchantNameCn} and TRADE_CODE = #{tradeCode}
        <if test="sid != null and sid != ''"> and id !=#{sid} </if>
    </select>
    <select id="selectMerchantCode" resultType="java.lang.Integer">
        select NVL(MAX(CAST(t.merchant_code AS NUMBER)),0) AS max_salary from T_BIZ_MERCHANT t
        WHERE t.merchant_code IS NOT NULL AND  REGEXP_LIKE(t.merchant_code, '^[0-9]+$') AND t.merchant_code != '' and TRADE_CODE = #{tradeCode}
    </select>

    <select id="getMaxSeqNo" resultType="java.lang.Integer">
        select COALESCE(MAX(SERIAL_NO),0) + 1 from T_BIZ_MERCHANT where TRADE_CODE = #{tradeCode};
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_merchant t where t.id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="getByMerchantCode" resultType="com.dcjet.cs.bi.model.BizMerchant">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_MERCHANT t
        WHERE TRADE_CODE = #{company} and MERCHANT_CODE = #{merchantCode}
    </select>

    <select id="getByMerchantCodes" resultType="com.dcjet.cs.bi.model.BizMerchant">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_MERCHANT t
        WHERE TRADE_CODE = #{company} and MERCHANT_CODE in
        <foreach collection="merchantCodes"  item="merchantCode" open="(" separator="," close=")"  >
            #{merchantCode}
        </foreach>
    </select>
    <select id="getMerchantByName" resultType="com.dcjet.cs.bi.model.BizMerchant">
        SELECT <include refid="Base_Column_List" />
        FROM
        T_BIZ_MERCHANT t
        WHERE TRADE_CODE = #{tradeCode} and merchant_name_cn =#{merchantNameCn}
    </select>
</mapper>
