package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.ExpenseIHead;
import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam;
import com.dcjet.cs.dto.bi.CostIContractParam;
import com.dcjet.cs.dto.bi.CostIShippingOrderParam;
import com.dcjet.cs.dto.params.CostTypeDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ExpenseIListMapper extends Mapper<ExpenseIList> {

    List<ExpenseIList> getList(ExpenseIList po);

    void deleteBySids(List<String> sids);

    List<CostIContractParam> getContractList(CostIContractParam costIContractParam);
    List<CostIContractParam> getContractListByBusinessType(CostIContractParam costIContractParam);

    List<CostIShippingOrderParam> getShippingOrderList(CostIShippingOrderParam costIShippingOrderParam);
    List<CostIShippingOrderParam> getShippingOrderListByBusinessType(CostIShippingOrderParam costIShippingOrderParam);

    ExpenseIList getSumDataByInvoiceSell(String headId);

    List<CostIContractParam> getContractHeadPayment(CostIContractParam costIContractParam);

    List<CostIShippingOrderParam> getShippingOrderHeadPayment(CostIShippingOrderParam costIShippingOrderParam);

    List<CostIContractParam> getContractHead(CostIContractParam costIContractParam);

    List<CostIShippingOrderParam> getShippingOrderHead(CostIShippingOrderParam costIShippingOrderParam);

    List<CostTypeDto> getCostType(CostIShippingOrderParam costIShippingOrderParam);

    List<ExpenseIList> getByHeadId(@Param("headId") String headId, @Param("tradeCode") String tradeCode);

    List<BizIAuxmatForContractHeadParam> getAuxmatContractPayment(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam);
    List<BizIAuxmatForContractHeadParam> getAuxmatContractList(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam);

    List<BizINonStateAuxmatAggrContractHeadParam>  getAuxnonContractPayment(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam);
    List<BizINonStateAuxmatAggrContractHeadParam>  getAuxnonContractPaymentCost(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam);
    List<BizINonStateAuxmatAggrContractHeadParam> getAuxnonContractList(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam);
}
