package com.dcjet.cs.payment.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter
@Getter
@Table(name = "T_BIZ_PAYMENT_NOTIFY_HEAD")
public class NotifyHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;

	@Transient
	private  String updateTimeFrom;
	@Transient
	private  String updateTimeTo;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     *
     */
	@Column(name = "note")
	private  String note;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "BIZ_TYPE")
	private  String bizType;

	/**
     * 收款方
     */
	@Column(name = "PAYEE")
	private  String payee;

	/**
     * 合同号
     */
	@Column(name = "CONTRACT_NO")
	private  String contractNo;
	/**
     * 进/出货单号
     */
	@Column(name = "ORDER_NUMBER")
	private  String orderNumber;
	/**
     * 付款金额
     */
	@Column(name = "PAY_AMT")
	private BigDecimal payAmt;

	/**
     * 币种
     */
	@Column(name = "CURR")
	private  String curr;
	/**
     * 预付标志
     */
	@Column(name = "PREPAY_FLAG")
	private  String prepayFlag;
	/**
     * 发送用友
     */
	@Column(name = "SEND_UFIDA")
	private  String sendUfida;
	/**
     * 单据状态
     */
	@Column(name = "DOC_STATUS")
	private  String docStatus;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CFM_TIME")
	private  Date cfmTime;
	/**
     * 业务日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "BIZ_DATE")
	private  Date bizDate;
	/**
	 * 收款银行
	 */
	@Column(name = "RECV_BANK")
	private  String recvBank;
	/**
	 * 收款方帐号
	 */
	@Column(name = "RECV_ACCT")
	private  String recvAcct;
	@Transient
	private  String recvTotal;
	/**
	 * RMB金额
	 */
	@Column(name = "PAY_AMT_RMB")
	private BigDecimal payAmtRmb;
	/**
	 * 汇率
	 */
	@Column(name = "RATE")
	private BigDecimal rate;
	@Transient
	private String rateStr;
	/**
	 * 单据号
	 */
	@Column(name = "DOC_NO")
	private  String docNo;
	/**
	 * 部门
	 */
	@Column(name = "DEPARTMENT")
	private  String department;


	@Transient
	private  BigDecimal payAmtTotal;
	@Transient
	private  BigDecimal payAmtRmbTotal;
	@Transient
	private  String payAmtRmbTotalStr;
	@Transient
	private  BigDecimal qtyTotal;
	@Transient
	private  BigDecimal includingTaxTotal;
	@Transient
	private  BigDecimal agencyFeesTotal;
	@Transient
	private  BigDecimal advPymtTotalAll;


	/**
	 * 是否红冲
	 */
	@Column(name = "red_flush")
	private  String redFlush;

	@Transient
	private  String goodsName;
	@Transient
	private  String invoiceNumber;


	@Transient
	private  Integer num;
	@Transient
	private  Date now;

}
