<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.payment.dao.RegistrationHeadMapper">
    <resultMap id="registrationHeadResultMap" type="com.dcjet.cs.payment.model.RegistrationHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />

        <result column="DOCUMENT_NO" property="documentNo" jdbcType="VARCHAR" />
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
        <result column="ADVANCE_FLAG" property="advanceFlag" jdbcType="VARCHAR" />
        <result column="DEPARTMENT" property="department" jdbcType="VARCHAR" />
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
        <result column="PAYER_NAME" property="payerName" jdbcType="VARCHAR" />
        <result column="ENTRUST_COMPANY" property="entrustCompany" jdbcType="VARCHAR" />
        <result column="BANK_FEE" property="bankFee" jdbcType="NUMERIC" />
        <result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="NUMERIC" />
        <result column="FINANCE_FLAG" property="financeFlag" jdbcType="VARCHAR" />
        <result column="REVERSAL_FLAG" property="reversalFlag" jdbcType="VARCHAR" />
        <result column="DOCUMENT_STATUS" property="documentStatus" jdbcType="VARCHAR" />
        <result column="BUSINESS_DATE" property="businessDate" jdbcType="TIMESTAMP" />
        <result column="REMARK" property="remark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
        sid,
        trade_code,
        insert_user,
        insert_time,
        insert_user_name,
        update_user,
        update_time,
        update_user_name,
        extend1,
        extend2,
        extend3,
        extend4,
        extend5,
        extend6,
        extend7,
        extend8,
        extend9,
        extend10,
        document_no,
        business_type,
        advance_flag,
        department,
        currency,
        payer_name,
        entrust_company,
        bank_fee,
        payment_amount,
        finance_flag,
        business_date,
        reversal_flag,
        document_status,
        confirm_time,
        remark,
        (SELECT LISTAGG(DISTINCT contract_no, ',') WITHIN
        GROUP (ORDER BY contract_no)
        FROM T_BIZ_REGISTRATION_LIST l
        WHERE l.HEAD_ID = t.sid
        ) AS contractNo,
        (SELECT LISTAGG(DISTINCT order_number, ',') WITHIN
        GROUP (ORDER BY order_number)
        FROM T_BIZ_REGISTRATION_LIST l
        WHERE l.HEAD_ID = t.sid
        ) AS orderNumber

    </sql>
    <sql id="condition">
        <if test="documentStatus != null and documentStatus != ''">
            and document_status = #{documentStatus}
        </if>
        <if test="documentStatus == null or documentStatus == ''">
            and document_status in ('0', '1')
        </if>
        <if test="businessType != null and businessType != ''">
            and business_type = #{businessType}
        </if>
        <if test="contractNo != null and contractNo != ''">
            and (SELECT LISTAGG(DISTINCT contract_no, ',') WITHIN
            GROUP (ORDER BY contract_no)
            FROM T_BIZ_REGISTRATION_LIST l
            WHERE l.HEAD_ID = t.sid
            ) like '%'|| #{contractNo} || '%'
        </if>
        <if test="orderNumber != null and orderNumber != ''">
            and (SELECT LISTAGG(DISTINCT order_number, ',') WITHIN
            GROUP (ORDER BY order_number)
            FROM T_BIZ_REGISTRATION_LIST l
            WHERE l.HEAD_ID = t.sid
            ) like '%'|| #{orderNumber} || '%'
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.update_time >= to_timestamp(#{updateTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.update_time < DATEADD(day, 1, to_timestamp(#{updateTimeTo}, 'yyyy-MM-dd HH24:mi:ss'))]]>
        </if>
        <if test="updateUserName != null and updateUserName  != ''">
            AND t.update_user_name LIKE '%' || #{updateUserName} || '%'
        </if>

            and trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="registrationHeadResultMap" parameterType="com.dcjet.cs.payment.model.RegistrationHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_REGISTRATION_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.update_time,t.insert_time) desc
    </select>
    <select id="getPlanListPaged" resultMap="registrationHeadResultMap" parameterType="com.dcjet.cs.payment.model.RegistrationHead">
        <choose>
        <when test="businessType == '2'.toString()">
        WITH PlanSummary AS (
        SELECT
        p.contract_no,
        max(l.curr) as curr,
        COALESCE(SUM(l.amount), 0) AS decTotal,
        COALESCE(SUM(l.qty), 0) AS qty,
        max(l.unit) as unit
        FROM t_biz_i_auxmat_buy_contract p
        LEFT JOIN t_biz_i_auxmat_buy_contract_list l ON l.HEAD_ID = p.ID
        WHERE p.STATUS = '1' and p.trade_code = #{tradeCode}
        GROUP BY p.contract_no
        )
        SELECT
        contract_no AS contract_no,
        curr,
        decTotal,
        qty AS qty,
        unit AS unit
        FROM PlanSummary
        <where>
        <if test="contractNo != null and contractNo != ''">
            and contract_no like '%'|| #{contractNo} || '%'
        </if>
            and contract_no not in (select distinct contract_no
            from T_BIZ_REGISTRATION_LIST l
            left join T_BIZ_REGISTRATION_HEAD h on l.HEAD_ID = h.SID
            where l.trade_code = #{tradeCode}
            AND l.contract_no IS NOT NULL
            and h.DOCUMENT_STATUS != '2')
        </where>
        ORDER BY contract_no DESC
        </when>
            <when test="businessType == '6'.toString()">
                WITH PlanSummary AS (
                SELECT
                p.CONTRACT_NO,
                p.CURRENCY as curr,
                COALESCE(SUM(l.amount), 0) AS decTotal,
                COALESCE(SUM(l.qty), 0) AS qty,
                max(l.unit) as unit
                FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD p
                LEFT JOIN T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l ON l.HEAD_ID = p.ID
                WHERE p.STATUS = '1' and p.trade_code = #{tradeCode}
                GROUP BY p.contract_no,p.CURRENCY
                )
                SELECT
                contract_no AS contract_no,
                curr,
                decTotal,
                qty AS qty,
                unit AS unit
                FROM PlanSummary
                <where>
                    <if test="contractNo != null and contractNo != ''">
                        and contract_no like '%'|| #{contractNo} || '%'
                    </if>
                    and contract_no not in (select distinct contract_no
                    from T_BIZ_REGISTRATION_LIST l
                    left join T_BIZ_REGISTRATION_HEAD h on l.HEAD_ID = h.SID
                    where l.trade_code = #{tradeCode}
                    AND l.contract_no IS NOT NULL
                    and h.DOCUMENT_STATUS != '2')
                </where>
                ORDER BY contract_no DESC
            </when>
        </choose>
    </select>
    <select id="checkContractNoExits" resultType="java.lang.Integer">
        select count(1) from T_BIZ_REGISTRATION_HEAD t
        where
            t.contract_no = #{contractNo} and t.DOCUMENT_STATUS != '2'
        <if test="sid != null and sid != ''">
            and t.sid != #{sid}
        </if>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_REGISTRATION_HEAD t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        ;
        delete from T_BIZ_REGISTRATION_LIST t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <update id="updateContractAmountAndQty">
        <![CDATA[
        UPDATE T_BIZ_REGISTRATION_HEAD h
        SET
            total_amount = COALESCE(
                    (SELECT SUM(l.total_value)
                     FROM T_BIZ_REGISTRATION_LIST l
                     WHERE l.HEAD_ID = h.SID),
                    0
                           ),
            total_quantity =
                CASE WHEN COALESCE(
                        (SELECT SUM(l.contract_quantity)
                         FROM T_BIZ_REGISTRATION_LIST l
                         WHERE l.HEAD_ID = h.SID),
                        0
                          ) <= 999999999
                then
                COALESCE(
                    (SELECT SUM(l.contract_quantity)
                     FROM T_BIZ_REGISTRATION_LIST l
                     WHERE l.HEAD_ID = h.SID),
                    0
                             )
                else null end
        where h.sid = #{sid}
        ]]>
    </update>
    <select id="getAllListAmountAndQty" resultType="com.dcjet.cs.payment.model.RegistrationHead">
        SELECT
            COALESCE(SUM(l.total_value), 0) as total_amount,
            COALESCE(SUM(l.contract_quantity),0) as total_quantity
        FROM T_BIZ_REGISTRATION_LIST l
        WHERE l.HEAD_ID = #{sid}
    </select>
    <select id="checkContractIsUsed" resultType="java.lang.Integer">
        select count(1) from  T_BIZ_I_ORDER_LIST l
            left join T_BIZ_I_ORDER_HEAD h on l.head_id = h.sid
        where h.DOCUMENT_STATUS != '2'
            and l.contract_no = #{contractNo}
    </select>
    <select id="checkStatusByContractNo" resultType="java.lang.Integer">
        select count(1) from T_BIZ_REGISTRATION_HEAD
        where contract_no = (
            select contract_no from T_BIZ_REGISTRATION_HEAD where sid = #{sid}
            )
        and DOCUMENT_STATUS != '2'
    </select>
    <update id="updateCancelByContract">
        UPDATE T_BIZ_REGISTRATION_HEAD h
        SET DOCUMENT_STATUS = '2'
        where contract_no = #{contractNo}
        and trade_code = #{tradeCode}
    </update>
    <select id="checkCanDelBySids" resultType="java.lang.Integer">
        select count(1)
        from T_BIZ_REGISTRATION_HEAD t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and t.DOCUMENT_STATUS != '0'
    </select>
    <select id="checkContractIsUsedBySids" resultType="java.lang.Integer">
        select count(1) from  T_BIZ_I_ORDER_LIST l
                left join T_BIZ_I_ORDER_HEAD h on l.head_id = h.sid
        where h.DOCUMENT_STATUS != '2'
            and l.contract_no in (
            select contract_no from T_BIZ_REGISTRATION_HEAD t
                where t.SID in
                <foreach collection="list"  item="item" open="(" separator="," close=")"  >
                    #{item}
                </foreach>
            )
    </select>
    <select id="getMaxVersionNoByContract" resultType="com.dcjet.cs.payment.model.RegistrationHead">
        select sid,VERSION_NO  from T_BIZ_REGISTRATION_HEAD
        where contract_no = #{contractNo}
          and trade_code = #{tradeCode}
        order by VERSION_NO desc limit 1
    </select>
    <select id="getSellerList" resultType="com.dcjet.cs.payment.model.RegistrationHead">
        SELECT
            t.seller,
            m.MERCHANT_NAME_CN as buyer
        FROM
        T_BIZ_REGISTRATION_HEAD t
        left join T_BIZ_MERCHANT m on t.SELLER = m.MERCHANT_CODE and t.TRADE_CODE = m.TRADE_CODE
        <where>
            <if test="tradeCode != null and tradeCode != ''">
                and t.trade_code = #{tradeCode}
            </if>
        </where>
        order by t.insert_time desc
    </select>
    <select id="getBuyerCodeByName" resultType="java.lang.String">
        select MERCHANT_CODE from T_BIZ_MERCHANT where MERCHANT_NAME_CN = #{name} and TRADE_CODE = #{tradeCode}
    </select>


    <select id="getDocNo" resultType="java.lang.String">
        select MAX(SUBSTR(document_no, LENGTH(document_no) - 2, 3)::NUMERIC) as documentNo
        from T_BIZ_REGISTRATION_HEAD
        where trade_code = #{tradeCode} and document_no like  '%'|| #{docNo} || '%'
    </select>
</mapper>
