package com.dcjet.cs.payment.service;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.dec.model.OBillBJck;
import com.dcjet.cs.dec.model.OBillJck;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import com.dcjet.cs.payment.dao.NotifyListMapper;
import com.dcjet.cs.payment.model.NotifyHead;
import com.dcjet.cs.payment.model.NotifyList;
import com.dcjet.cs.service.ThirdPartyDbService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.payment.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.payment.dao.BizPaymentSettlementMapper;
import com.dcjet.cs.payment.mapper.BizPaymentSettlementDtoMapper;
import com.dcjet.cs.payment.model.BizPaymentSettlement;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import dm.jdbc.util.StringUtil;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-26
 */
@Service
public class BizPaymentSettlementService extends BaseService<BizPaymentSettlement> {
    @Resource
    private BizPaymentSettlementMapper bizPaymentSettlementMapper;
    @Resource
    private BizPaymentSettlementDtoMapper bizPaymentSettlementDtoMapper;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    @Resource
    private GwstdHttpConfigMapper gwstdHttpConfigMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private NotifyListMapper notifyListMapper;
    @Resource
    private ThirdPartyDbService thirdPartyDbService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Override
    public Mapper<BizPaymentSettlement> getMapper() {
        return bizPaymentSettlementMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizPaymentSettlementParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizPaymentSettlementDto>> getListPaged(BizPaymentSettlementParam bizPaymentSettlementParam, PageParam pageParam) {
        // 启用分页查询
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementDtoMapper.toPo(bizPaymentSettlementParam);
        Page<BizPaymentSettlement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizPaymentSettlementMapper.getList(bizPaymentSettlement));
        List<BizPaymentSettlementDto> bizPaymentSettlementDtos = page.getResult().stream().map(head -> {
            BizPaymentSettlementDto dto = bizPaymentSettlementDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizPaymentSettlementDto>> paged = ResultObject.createInstance(bizPaymentSettlementDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizPaymentSettlementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPaymentSettlementDto insert(BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementDtoMapper.toPo(bizPaymentSettlementParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizPaymentSettlement.setSid(sid);
        bizPaymentSettlement.setInsertUser(userInfo.getUserNo());
        bizPaymentSettlement.setInsertTime(new Date());
        bizPaymentSettlement.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = bizPaymentSettlementMapper.insert(bizPaymentSettlement);
        return  insertStatus > 0 ? bizPaymentSettlementDtoMapper.toDto(bizPaymentSettlement) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizPaymentSettlementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPaymentSettlementDto update(BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(bizPaymentSettlementParam.getSid());
        bizPaymentSettlementDtoMapper.updatePo(bizPaymentSettlementParam, bizPaymentSettlement);
        bizPaymentSettlement.setUpdateUser(userInfo.getUserNo());
        bizPaymentSettlement.setUpdateUserName(userInfo.getUserName());
        bizPaymentSettlement.setTradeCode(userInfo.getCompany());
        bizPaymentSettlement.setUpdateTime(new Date());
        // 更新数据
        int update = bizPaymentSettlementMapper.updateByPrimaryKey(bizPaymentSettlement);
        return update > 0 ? bizPaymentSettlementDtoMapper.toDto(bizPaymentSettlement) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizPaymentSettlementMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizPaymentSettlementDto> selectAll(BizPaymentSettlementParam exportParam, UserInfoToken userInfo) {
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementDtoMapper.toPo(exportParam);
        // bizPaymentSettlement.setTradeCode(userInfo.getCompany());
        List<BizPaymentSettlementDto> bizPaymentSettlementDtos = new ArrayList<>();
        List<BizPaymentSettlement> bizPaymentSettlements = bizPaymentSettlementMapper.getList(bizPaymentSettlement);
        if (CollectionUtils.isNotEmpty(bizPaymentSettlements)) {
            bizPaymentSettlementDtos = bizPaymentSettlements.stream().map(head -> {
                BizPaymentSettlementDto dto = bizPaymentSettlementDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizPaymentSettlementDtos;
    }

    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(sid);
        if (bizPaymentSettlement == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        // 更新状态为1 确认
        bizPaymentSettlement.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizPaymentSettlement.setConfirmTime(new Date());
        updateStatus(bizPaymentSettlement); // 调用更新状态的方法
        resultObject.setData(bizPaymentSettlement);

        List<NotifyList> list = notifyListMapper.select(new NotifyList() {{
            setTradeCode(userInfo.getCompany());
            setHeadId(bizPaymentSettlement.getHeadId());
        }});


        //用友----付款结算
        GwstdHttpConfig gwstdHttpConfig = gwstdHttpConfigMapper.selectByType("YonyouState");
        if(gwstdHttpConfig != null && "1".equals(gwstdHttpConfig.getServiceUrl())) {
            sendPaymentYonyou(bizPaymentSettlement,list,userInfo);
        }
        return resultObject;
    }
    private void sendPaymentYonyou(BizPaymentSettlement head, List<NotifyList> list, UserInfoToken userInfo) {
        String id = UUID.randomUUID().toString().replace("-", StringUtils.EMPTY);
        OBillJck oBillJck = headMessagePayment(head, list, userInfo,id);
        List<OBillBJck> oBillBJcks = listMessagePayment(head, list, userInfo,id);
        //存储信息
        try {
            Map<String, Object> stringObjectMap = thirdPartyDbService.convertToMap(oBillJck);
            thirdPartyDbService.insertData("O_BILL_JCK",stringObjectMap);
            List<Map<String, Object>> maps = thirdPartyDbService.convertListToMapList(oBillBJcks);
            thirdPartyDbService.batchInsertData("O_BILL_B_JCK",maps);
        } catch (IllegalAccessException e) {
            throw new RuntimeException("发生用友失败！");
        }
    }

    private List<OBillBJck> listMessagePayment(BizPaymentSettlement head, List<NotifyList> list, UserInfoToken userInfo, String id) {
        List<OBillBJck> oBillBJcks = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            long[] seqNos = this.thirdPartyDbService.getSequenceNextValue("O_BILL_B_JCK_SEQ", list.size());
            for (int i = 0; i < list.size(); i++) {
                NotifyList notifyList = list.get(i);
                OBillBJck oBillBJck = new OBillBJck();
                //--字段名称---字段名-----------取值
                //关联主表外键  billid
                oBillBJck.setBillid(id);
                //存货编码	  inventory
                List<BizMaterialInformation> select = bizMaterialInformationMapper.select(new BizMaterialInformation() {{
                    setTradeCode(userInfo.getCompany());
                    setGname(notifyList.getGoodsName());
                }});
                if(CollectionUtils.isNotEmpty(select)){
                    oBillBJck.setInventory(select.get(0).getBarCode());
                }
                //数量	      tnumber
                oBillBJck.setTnumber(new BigDecimal(0));
                //计量单位	  mainunit
                List<BaseInfoCustomerParams> selectCurr = baseInfoCustomerParamsMapper.select(new BaseInfoCustomerParams() {{
                    setCustomParamCode(notifyList.getUnit());
                    setTradeCode(userInfo.getCompany());
                    setParamsType("UNIT");
                }});
                if(CollectionUtils.isNotEmpty(selectCurr)){
                    oBillBJck.setMainunit(selectCurr.get(0).getUnitYonyou());
                    oBillBJck.setNameMainunit(selectCurr.get(0).getParamsName());
                }
                //单价	      price
                oBillBJck.setPrice(new BigDecimal(0));
                //不含税金额	  notaxmoney
                oBillBJck.setNotaxmoney(notifyList.getPayAmtRmb());
                //金额	      totalmoney
                oBillBJck.setTotalmoney(notifyList.getPayAmtRmb());
                oBillBJck.setTaxmoney(new BigDecimal(0));

                //存货名称	  name_invmandoc
                oBillBJck.setNameInvmandoc(notifyList.getGoodsName());
                //系统时间	  ts
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(new Date());
                oBillBJck.setTs(formattedDate);
                //客商编码	  shipcustname
                oBillBJck.setShipcustname(head.getCustomer());
                //发票号	      billno_bt
                oBillBJck.setBillnoBt(notifyList.getInvoiceNumber());
                //客商编码	  pk_shipcust
                if(StringUtil.isNotEmpty(head.getCustomer())){
                    BizMerchant bizMerchant = new BizMerchant();
                    bizMerchant.setTradeCode(userInfo.getCompany());
                    bizMerchant.setMerchantCode(head.getCustomer());
                    //查询出全部的客商信息
                    List<BizMerchant> select1 = bizMerchantMapper.select(bizMerchant);
                    oBillBJck.setPkShipcust(select1.get(0).getFinanceCode());
                }
                oBillBJck.setMqLsh((int) seqNos[i]);
                oBillBJck.setCurrentrate(head.getExchangeRate());
                oBillBJck.setBbje(notifyList.getPayAmtRmb());

                //收支项目编码 costsubj
                oBillBJck.setCostsubj("2001");
                //收支项目名称 name_costsubj
                oBillBJck.setNameCostsubj("货款");
                //           mq_op
                oBillBJck.setMqOp("i");
                //           mq_st
                oBillBJck.setMqSt("0");
                //           mq_count
                oBillBJck.setMqCount(1);
                //           pk_corp
                oBillBJck.setPkCorp("1022");
                oBillBJck.setDr(0);
                oBillBJck.setCurrentrate(new BigDecimal(1));
                oBillBJcks.add(oBillBJck);
            }
        }
        return oBillBJcks;
    }

    private OBillJck headMessagePayment(BizPaymentSettlement head, List<NotifyList> list, UserInfoToken userInfo,String id) {
        OBillJck oBillJck = new OBillJck();
//--------字段名称----字段名------------取值
        //单据主键　	billid
        oBillJck.setBillid(id);
        //单据编号　	billcode
        oBillJck.setBillcode(head.getBillId());
        //单据日期	billdate
        if(head.getBusinessDate() != null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(head.getBusinessDate());
            oBillJck.setBilldate(formattedDate);
        }
        //业务人员	person
        oBillJck.setPerson(head.getInsertUser());
        //制单人员	maker
        oBillJck.setMaker(head.getInsertUser());
        //客商编码	cust
        oBillJck.setCust(head.getCustomer());
        //业务员名称	name_psndoc
        oBillJck.setNamePsndoc(head.getInsertUserName());
        //操作员名称	name_operator
        oBillJck.setNameOperator(head.getInsertUserName());
        //客商名称	name_cumandoc
        if(StringUtil.isNotEmpty(head.getCustomer())){
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            bizMerchant.setMerchantCode(head.getCustomer());
            //查询出全部的客商信息
            List<BizMerchant> select1 = bizMerchantMapper.select(bizMerchant);
            oBillJck.setNameCumandoc(select1.get(0).getMerchantNameCn());
        }
        //系统时间	ts               接口数据发送时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(new Date());
        oBillJck.setTs(formattedDate);
        oBillJck.setHth(head.getContractNo());
        List<BaseInfoCustomerParams> selectCurr = baseInfoCustomerParamsMapper.select(new BaseInfoCustomerParams() {{
            setCustomParamCode(head.getCurr());
            setTradeCode(userInfo.getCompany());
            setParamsType("CURR");
        }});
        if(CollectionUtils.isNotEmpty(selectCurr)){
            oBillJck.setCurrtypecode(selectCurr.get(0).getParamsCode());
        }
        oBillJck.setCurrtypename(head.getCurr());
        oBillJck.setTotalmoney(head.getOriginalAmount());
        oBillJck.setBillno(head.getForeignInvoiceNo());
        if(CollectionUtils.isNotEmpty(list)) {
            //表体行数	row_count        接口表赋值：表体数据的行数
            oBillJck.setRowCount(list.size());
        }
        oBillJck.setCurrentrate(head.getExchangeRate());
        oBillJck.setBbje(head.getRmbAmount());
        oBillJck.setMqLsh((int) this.thirdPartyDbService.getSequenceNextValue("O_BILL_JCK_SEQ", 1)[0]);
//--------字段名称-----字段名-----------默认值
        //单据类型	 rd_type
        oBillJck.setRdType("A3");
        //库存组织	 rdcenter
        oBillJck.setRdcenter("JCK01");
        //公司名称	 company
        oBillJck.setCompany("中国烟草上海进出口有限责任公司");
        //部门编码	 deptdoc
        oBillJck.setDeptdoc("04");
        //库存组织名称 name_calbody
        oBillJck.setNameCalbody("个别计价");
        //部门名称	 name_deptdoc
        oBillJck.setNameDeptdoc("业务一部");
        //           mq_op             i
        oBillJck.setMqOp("1");
        //           mq_st             0
        oBillJck.setMqSt("0");
        //           mq_count          1
        oBillJck.setMqCount(1);
        //           pk_corp           1022
        oBillJck.setPkCorp("1022");
        oBillJck.setDr(0);

        return oBillJck;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(BizPaymentSettlement bizPaymentSettlement) {
        bizPaymentSettlementMapper.updateStatus(bizPaymentSettlement); // 调用更新状态的方法
    }

    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(sid);
        if (bizPaymentSettlement == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        // 更新状态为2（作废）
        bizPaymentSettlement.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizPaymentSettlement.setConfirmTime(null);
        updateStatus(bizPaymentSettlement); // 调用更新状态的方法

        return resultObject;
    }


    public ResultObject backDataStatus(BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(bizPaymentSettlementParam.getSid());
        if (bizPaymentSettlement == null) {
            throw new ErrorException(400, "货款结算表头数据不存在，请刷新");
        }
        bizPaymentSettlement.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizPaymentSettlement.setConfirmTime(null);
        bizPaymentSettlementMapper.updateByPrimaryKey(bizPaymentSettlement);
        return result;
    }

}
