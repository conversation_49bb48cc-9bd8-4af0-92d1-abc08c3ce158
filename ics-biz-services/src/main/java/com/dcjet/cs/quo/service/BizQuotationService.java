package com.dcjet.cs.quo.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.dto.bi.BizMaterialInformationDto;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.quo.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.quo.dao.BizQuotationMapper;
import com.dcjet.cs.quo.mapper.BizQuotationDtoMapper;
import com.dcjet.cs.quo.model.BizQuotation;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.models.auth.In;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import xdoi18n.XdoI18nUtil;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-20
 */
@Service
public class BizQuotationService extends BaseService<BizQuotation> {
    @Resource
    private BizQuotationMapper bizQuotationMapper;
    @Resource
    private BizQuotationDtoMapper bizQuotationDtoMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Override
    public Mapper<BizQuotation> getMapper() {
        return bizQuotationMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizQuotationParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizQuotationDto>> getListPaged(BizQuotationParam bizQuotationParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizQuotation bizQuotation = bizQuotationDtoMapper.toPo(bizQuotationParam);
        bizQuotation.setTradeCode(userInfo.getCompany());
        Page<BizQuotation> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizQuotationMapper.getList(bizQuotation));
        List<BizQuotationDto> bizQuotationDtos = page.getResult().stream().map(head -> {
            BizQuotationDto dto = bizQuotationDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizQuotationDto>> paged = ResultObject.createInstance(bizQuotationDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizQuotationDto insert(BizQuotationParam bizQuotationParam, UserInfoToken userInfo) {
        BizQuotation bizQuotation = bizQuotationDtoMapper.toPo(bizQuotationParam);
        bizQuotation.setTradeCode(userInfo.getCompany());
        int check = bizQuotationMapper.checkKey(bizQuotation);
        if(check>0){
            throw new ErrorException(400, "商品名称+规格已经存在！");
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizQuotation.setSid(sid);
        bizQuotation.setCreateBy(userInfo.getUserNo());
        bizQuotation.setCreateTime(new Date());
        bizQuotation.setCreateUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = bizQuotationMapper.insert(bizQuotation);
        return  insertStatus > 0 ? bizQuotationDtoMapper.toDto(bizQuotation) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizQuotationDto update(BizQuotationParam bizQuotationParam, UserInfoToken userInfo) {
        BizQuotation bizQuotation = bizQuotationMapper.selectByPrimaryKey(bizQuotationParam.getSid());
        bizQuotationDtoMapper.updatePo(bizQuotationParam, bizQuotation);
        bizQuotation.setUpdateBy(userInfo.getUserNo());
        bizQuotation.setUpdateUserName(userInfo.getUserName());
        bizQuotation.setUpdateTime(new Date());

        int check = bizQuotationMapper.checkKey(bizQuotation);
        if(check>0){
            throw new ErrorException(400, "商品名称+规格已经存在！");
        }
        // 更新数据
        int update = bizQuotationMapper.updateByPrimaryKey(bizQuotation);
        return update > 0 ? bizQuotationDtoMapper.toDto(bizQuotation) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizQuotationMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizQuotationDto> selectAll(BizQuotationParam exportParam, UserInfoToken userInfo) {
        BizQuotation bizQuotation = bizQuotationDtoMapper.toPo(exportParam);
         bizQuotation.setTradeCode(userInfo.getCompany());
        List<BizQuotationDto> bizQuotationDtos = new ArrayList<>();
        List<BizQuotation> bizQuotations = bizQuotationMapper.getList(bizQuotation);
        if (CollectionUtils.isNotEmpty(bizQuotations)) {
            bizQuotationDtos = bizQuotations.stream().map(head -> {
                BizQuotationDto dto = bizQuotationDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizQuotationDtos;
    }

    public Boolean enable(List<String> sids, String enable, UserInfoToken userInfo) {
        if(ObjectUtils.isEmpty(sids)){
            if(enable.equals("1")){
                throw new ErrorException(400,"作废数据不存在！");
            }
            if(enable.equals("0")){
                throw new ErrorException(400,"重新启用数据不存在！");
            }
        }
        for (String sid : sids) {
            BizQuotation bizQuotation = bizQuotationMapper.selectByPrimaryKey(sid);
            if(!ObjectUtils.isEmpty(bizQuotation)){
                BizQuotation update = new BizQuotation();
                update.setSid(sid);
                update.setStatus(enable);
//                update.setUpdateBy(userInfo.getUserNo());
//                update.setUpdateTime(new Date());
                bizQuotationMapper.updateByPrimaryKeySelective(update);
            }
        }
        return true;
    }

    /**
     * 根据交易代码获取物料信息(用于购销合同)
     * @param mat 物料信息
     * @return 物料信息列表
     */
    public List<BizQuotationDto> getMatForBuyContract(BizQuotationDto mat, UserInfoToken userInfo) {
        mat.setTradeCode(userInfo.getCompany());
        List<BizQuotation> materialList = bizQuotationMapper.getMatForBuyContract(mat);
        if (CollectionUtils.isEmpty(materialList)) {
            return new ArrayList<>();
        }
        return materialList.stream().map(material -> {
            BizQuotationDto dto = bizQuotationDtoMapper.toDto(material);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 打印卷烟纸格式报价表
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    public ResponseEntity printTippingPaper(BizQuotationParam bizQuotationParam, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("接装纸格式报价表");

        List<String> sids = bizQuotationParam.getSids();
        if (CollectionUtils.isEmpty(sids)){
            throw new ErrorException(400, "请选择要打印的数据！");
        }

        //获取供应商
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        Map<String, String> merchantMap = createMerchantMap(bizMerchants);

        // 收集所有生成的Excel文件路径
        List<String> excelFilesCover = new ArrayList<>();
        List<String> excelFiles = new ArrayList<>();

        Integer totalCount = sids.size();
        Integer pageNum = 1;
        int totalPage = (int) Math.ceil((double) totalCount / 3);
        int currentPage = 1;

        List<BizQuotation> pageList = new ArrayList<>();

        for (String sid : sids) {
            //封页 每3个一页
            String templateNameCover = "接装纸格式报价表-封页.xlsx";

            String templateName = "接装纸格式报价表-附表.xlsx";
            String fileName = UUID.randomUUID() + ".xlsx";
            BizQuotation bizQuotation = bizQuotationMapper.selectByPrimaryKey(sid);
            bizQuotation.setUnit(pCodeHolder.getValue(PCodeType.UNIT, bizQuotation.getUnit()));
            bizQuotation.setMerchantCode(merchantMap.get(bizQuotation.getMerchantCode()));
            bizQuotation.setCreateUserName("填报人："+userInfo.getUserName()+"   部门负责人：");

            pageList.add(bizQuotation);
            if (pageList.size() == 3 || pageNum.equals(totalCount)){
                String fileNameCover = UUID.randomUUID() + ".xlsx";
                //封页数据组装
                BizQuotation cover = coverPageDataAssembly(pageList, pageNum);
                cover.setCreateTime(new Date());
                cover.setPageStr("第" + currentPage + "页，共" + totalPage + "页");
                String exportFileCover = exportService.export(Collections.singletonList(cover), new ArrayList<BizQuotation>(),fileNameCover, templateNameCover);
                excelFilesCover.add(exportFileCover);
                pageList.clear();
                currentPage++;
            }

            //附表 数据组装
            String exportFileName = exportService.export(Collections.singletonList(bizQuotation), new ArrayList<BizQuotation>(),fileName, templateName);
            if (exportFileName != null && !exportFileName.isEmpty()) {
                excelFiles.add(exportFileName);
            }
            pageNum ++;
        }

        // 如果没有生成任何文件，抛出异常
        if (excelFiles.isEmpty() || excelFilesCover.isEmpty()) {
            throw new ErrorException(400, "没有生成任何Excel文件！");
        }

        String finalExportFileNameCover;
        if (excelFilesCover.size() == 1) {
            // 只有一个文件，直接使用
            finalExportFileNameCover = excelFilesCover.get(0);
        } else {
            // 多个文件，合并为多个sheet
            finalExportFileNameCover = exportService.mergeExcelFilesToSheets(excelFilesCover, outName);
        }

        String finalExportFileName;
        if (excelFiles.size() == 1) {
            // 只有一个文件，直接使用
            finalExportFileName = excelFiles.get(0);
        } else {
            // 多个文件，合并为多个sheet
            finalExportFileName = exportService.mergeExcelFilesToSheets(excelFiles, outName);
        }
        //合并
        exportService.mergeExcel(finalExportFileNameCover, finalExportFileName);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(finalExportFileNameCover));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName + ".xlsx", "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
//        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
//                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
    /**
     * 打印卷烟纸格式报价表
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    public ResponseEntity printCigarettePaper(BizQuotationParam bizQuotationParam, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("卷烟纸格式报价表");

        List<String> sids = bizQuotationParam.getSids();
        if (CollectionUtils.isEmpty(sids)){
            throw new ErrorException(400, "请选择要打印的数据！");
        }

        //获取供应商
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        Map<String, String> merchantMap = createMerchantMap(bizMerchants);

        // 收集所有生成的Excel文件路径
        List<String> excelFilesCover = new ArrayList<>();
        List<String> excelFiles = new ArrayList<>();

        Integer totalCount = sids.size();
        Integer pageNum = 1;
        int totalPage = (int) Math.ceil((double) totalCount / 3);
        int currentPage = 1;

        List<BizQuotation> pageList = new ArrayList<>();

        for (String sid : sids) {
            //封页 每3个一页
            String templateNameCover = "卷烟纸格式报价表-封页.xlsx";

            String templateName = "卷烟纸格式报价表-附表.xlsx";
            String fileName = UUID.randomUUID().toString() + ".xlsx";
            BizQuotation bizQuotation = bizQuotationMapper.selectByPrimaryKey(sid);
            bizQuotation.setUnit(pCodeHolder.getValue(PCodeType.UNIT, bizQuotation.getUnit()));
            bizQuotation.setMerchantCode(merchantMap.get(bizQuotation.getMerchantCode()));

            pageList.add(bizQuotation);
            if (pageList.size() == 3 || pageNum.equals(totalCount)){
                String fileNameCover = UUID.randomUUID().toString() + ".xlsx";
                //封页数据组装
                BizQuotation cover = coverPageDataAssembly(pageList, pageNum);
                cover.setCreateTime(new Date());
                cover.setPageStr("第" + currentPage + "页，共" + totalPage + "页");
                String exportFileCover = exportService.export(Collections.singletonList(cover), new ArrayList<BizQuotation>(),fileNameCover, templateNameCover);
                excelFilesCover.add(exportFileCover);
                pageList.clear();
                currentPage++;
            }

            //附表 数据组装
            String exportFileName = exportService.export(Collections.singletonList(bizQuotation), new ArrayList<BizQuotation>(),fileName, templateName);
            if (exportFileName != null && !exportFileName.isEmpty()) {
                excelFiles.add(exportFileName);
            }
            pageNum ++;
        }

        // 如果没有生成任何文件，抛出异常
        if (excelFiles.isEmpty() || excelFilesCover.isEmpty()) {
            throw new ErrorException(400, "没有生成任何Excel文件！");
        }

        String finalExportFileNameCover;
        if (excelFilesCover.size() == 1) {
            // 只有一个文件，直接使用
            finalExportFileNameCover = excelFilesCover.get(0);
        } else {
            // 多个文件，合并为多个sheet
            finalExportFileNameCover = exportService.mergeExcelFilesToSheets(excelFilesCover, outName);
        }

        String finalExportFileName;
        if (excelFiles.size() == 1) {
            // 只有一个文件，直接使用
            finalExportFileName = excelFiles.get(0);
        } else {
            // 多个文件，合并为多个sheet
            finalExportFileName = exportService.mergeExcelFilesToSheets(excelFiles, outName);
        }
        //合并
        exportService.mergeExcel(finalExportFileNameCover, finalExportFileName);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(finalExportFileNameCover));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName + ".xlsx", "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
//        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
//                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    private BizQuotation coverPageDataAssembly(List<BizQuotation> pageList, Integer pageNum) {
        BizQuotation bizQuotation = new BizQuotation();
        bizQuotation.setGName(pageList.get(0).getGName());
        bizQuotation.setUnit(pageList.get(0).getUnit());
        bizQuotation.setProductModel(pageList.get(0).getProductModel());
        bizQuotation.setMerchantCode(pageList.get(0).getMerchantCode());
        bizQuotation.setImportUnitPriceStr("USD" + NumberFormatterUtils.formatNumber(pageList.get(0).getImportUnitPrice()) + "/MT CIF SHANGHAI");
        bizQuotation.setPriceRmbStr(NumberFormatterUtils.formatNumber(pageList.get(0).getPriceRmb()) + "/" + pageList.get(0).getUnit());
        if (pageList.size() == 1) {
            return bizQuotation;
        }
        bizQuotation.setGName1(pageList.get(1).getGName());
        bizQuotation.setUnit1(pageList.get(1).getUnit());
        bizQuotation.setProductModel1(pageList.get(1).getProductModel());
        bizQuotation.setMerchantCode1(pageList.get(1).getMerchantCode());
        bizQuotation.setImportUnitPriceStr1("USD" + NumberFormatterUtils.formatNumber(pageList.get(1).getImportUnitPrice()) + "/MT CIF SHANGHAI");
        bizQuotation.setPriceRmbStr1(NumberFormatterUtils.formatNumber(pageList.get(1).getPriceRmb()) + "/" + pageList.get(1).getUnit());
        if (pageList.size() == 2) {
            return bizQuotation;
        }
        bizQuotation.setGName2(pageList.get(2).getGName());
        bizQuotation.setUnit2(pageList.get(2).getUnit());
        bizQuotation.setProductModel2(pageList.get(2).getProductModel());
        bizQuotation.setMerchantCode2(pageList.get(2).getMerchantCode());
        bizQuotation.setImportUnitPriceStr2("USD" + NumberFormatterUtils.formatNumber(pageList.get(2).getImportUnitPrice()) + "/MT CIF SHANGHAI");
        bizQuotation.setPriceRmbStr2(NumberFormatterUtils.formatNumber(pageList.get(2).getPriceRmb()) + "/" + pageList.get(2).getUnit());
        return bizQuotation;
    }

    /**
     * 创建商户映射表，处理重复key的情况
     * @param bizMerchants 商户列表
     * @return 商户编码到中文名称的映射
     */
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
}
