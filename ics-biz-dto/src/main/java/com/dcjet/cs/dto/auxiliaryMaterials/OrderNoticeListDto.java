package com.dcjet.cs.dto.auxiliaryMaterials;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel("订货通知表体传输模型")
public class OrderNoticeListDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 上一版本id
     */
    @ApiModelProperty("上一版本id")
    private String parentId;

    /**
     * 表头id
     */
    @ApiModelProperty("表头id")
    private String headId;

    /**
     * 购销合同表头关联ID
     */
    @ApiModelProperty("购销合同表头关联ID")
    private String buyHeadId;

    /**
     * 购销合同表体关联ID
     */
    @ApiModelProperty("购销合同表体关联ID")
    private String buyListId;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String specification;

    /**
     * 克重
     */
    @ApiModelProperty("克重")
    private BigDecimal weight;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplier;

    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;

    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    private String transportMode;

    /**
     * 港口
     */
    @ApiModelProperty("港口")
    private String port;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 计量单位
     */
    @ApiModelProperty("计量单位")
    private String measureUnit;

    /**
     * 要求到货日
     */
    @ApiModelProperty("要求到货日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date requestDeliveryDate;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;
}