package com.dcjet.cs.dto.auxiliaryMaterials;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel("订货通知新增传输模型")
public class OrderNoticeAddDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private String businessType;


    /**
     * 购销合同号
     */
    @ApiModelProperty("购销合同号")
    private String purchaseSalesContractNo;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 制单人
     */
    @ApiModelProperty("制单人")
    private String createBy;

    /**
     * 制单人姓名
     */
    @ApiModelProperty("制单人姓名")
    private String createByName;

    /**
     * 制单时间
     */
    @ApiModelProperty("制单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    /**
     * 单据状态 0编制 1确认 2作废
     */
    @ApiModelProperty("单据状态")
    private String dataStatus;

    /**
     * 确认时间
     */
    @ApiModelProperty("确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    /**
     * 审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回
     */
    @ApiModelProperty("审批状态")
    private String apprStatus;

    /**
     * 表体列表
     */
    @ApiModelProperty("表体列表")
    private List<OrderNoticeListDto> bodyList;
}