package com.dcjet.cs.dto.warehouse;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2025-5-22
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class ConversionPrintOutBoundReceiptHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 出库回单编号
      */
    @ApiModelProperty("出库回单编号")
	private  String storeENo;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 提货人
      */
    @ApiModelProperty("提货人")
	private  String consignee;
	/**
      * 出库日期
      */
    @ApiModelProperty("出库日期")
	private  String deliveryDate;
	/**
      * 金额
      */
    @ApiModelProperty("金额")
	private  BigDecimal productAmountTotal;
	/**
      * 关税
      */
    @ApiModelProperty("关税")
	private  BigDecimal tariffPrice;
	/**
      * 保险费用
      */
    @ApiModelProperty("保险费用")
	private  BigDecimal insuranceFee;
	/**
      * 代理费用
      */
    @ApiModelProperty("代理费用")
	private  BigDecimal agentFee;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	//总金额
	private BigDecimal total;
	//总数量
	private BigDecimal qtyDeli;
	private BigDecimal qtyIss;


}
