package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizINonStateAuxmatAggrContractHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String id;
	/**
     * 业务类型
     */
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 买方
     */
	@XdoSize(max = 400, message = "买方长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("买方")
	private  String buyer;
	/**
     * 供应商
     */
	@ApiModelProperty("供应商")
	private  String supplier;
	/**
     * 签约日期
     */
	@ApiModelProperty("签约日期")
	private  Date signingDate;
	/**
     * 国内委托方
     */
	@ApiModelProperty("国内委托方")
	private  String domesticPrincipal;
	/**
     * 运输方式
     */
	@XdoSize(max = 20, message = "运输方式长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("运输方式")
	private  String transportMode;
	/**
     * 合同生效期
     */
	@ApiModelProperty("合同生效期")
	private  Date effectiveDate;
	/**
     * 合同有效期
     */
	@ApiModelProperty("合同有效期")
	private  Date expiryDate;
	/**
     * 签约地点(中文)
     */
	@XdoSize(max = 100, message = "签约地点(中文)长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点(中文)")
	private  String signingLocationCn;
	/**
     * 签约地点(英文)
     */
	@XdoSize(max = 100, message = "签约地点(英文)长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点(英文)")
	private  String signingLocationEn;
	/**
     * 装运港
     */
	@XdoSize(max = 100, message = "装运港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装运港")
	private  String portOfLoading;
	/**
     * 目的港
     */
	@XdoSize(max = 100, message = "目的港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("目的港")
	private  String portOfDestination;
	/**
     * 报关口岸
     */
	@XdoSize(max = 100, message = "报关口岸长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("报关口岸")
	private  String customsPort;
	/**
     * 付款方式
     */
	@XdoSize(max = 40, message = "付款方式长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("付款方式")
	private  String paymentMethod;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String currency;
	/**
     * 价格条款
     */
	@XdoSize(max = 40, message = "价格条款长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@XdoSize(max = 100, message = "价格条款对应港口长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款对应港口")
	private  String priceTermPort;
	/**
     * 建议授权签约人
     */
	@XdoSize(max = 60, message = "建议授权签约人长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("建议授权签约人")
	private  String suggestedSigner;
	/**
     * 短溢数%
     */
	@Digits(integer = 13, fraction = 6, message = "短溢数%必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("短溢数%")
	private  BigDecimal shortageOverflowPercent;
	/**
     * 备注
     */
	@XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String remarks;
	/**
     * 版本号
     */
	@XdoSize(max = 20, message = "版本号长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
     * 单据状态
     */
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@XdoSize(max = 20, message = "审批状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
     * 制单日期
     */
//	@NotNull(message="制单日期不能为空！")
	@ApiModelProperty("制单日期")
	private  Date createTime;
	/**
    * 制单日期-开始
    */
	@ApiModelProperty("制单日期-开始")
	private String createTimeFrom;
	/**
    * 制单日期-结束
    */
	@ApiModelProperty("制单日期-结束")
    private String createTimeTo;

	/**
	 * 是否划款通知保存过(0否;1是)
	 */
	@ApiModelProperty("是否划款通知保存过(0否;1是)")
	private String isTransferNotice;
	/**
	 * 协议编号
	 */
	@ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
	 * 签约日期
	 */
	@ApiModelProperty("签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date agreementSigningDate;
	/**
	 * 代理费率
	 */
	@ApiModelProperty("代理费率")
	private  BigDecimal agreementAgentFeeRate;
	/**
	 * 建议授权签约人
	 */
	@ApiModelProperty("建议授权签约人")
	private  String agreementSuggestedSigner;
	/**
	 * 协议条款
	 */
	@ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
	 * 协议备注
	 */
	@ApiModelProperty("协议备注")
	private  String agreementRemarks;

	private String tradeCode;
	private String headId;
	private String partyB;
	private String curr;
	private BigDecimal decTotal;
	private BigDecimal decPrice;
	private BigDecimal qty;
	private String unit;
	private String merchandiseCategories;
	private String productName;
	private List<String> sids;
	private String expenseType;
	//税额金额
	private BigDecimal taxAmount;
	//无税金额
	private BigDecimal noTaxAmount;
	//费用金额
	private String amount;
	//是否分摊
	private String apportionment;
	//分摊方式
	private String methodAllocation;

	private String type;
}
