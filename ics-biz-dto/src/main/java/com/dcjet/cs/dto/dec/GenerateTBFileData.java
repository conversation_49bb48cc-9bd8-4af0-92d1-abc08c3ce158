package com.dcjet.cs.dto.dec;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Getter
@Setter
public class GenerateTBFileData implements Serializable {

    // 制单人   邵海英   （制单人）
    private String createBy;


    // 编号  编号：2024NGEQ/912608FR04 (进货单号)
    private String code;


    // Cigarette paper   取表体任一商品名称，关联物料信息对应的英文全称
    private String cigarettePaper;

    //  43.52 MT 汇总表体数量+MT
    private String totalQuantity;


    //  CFR(价格条款)
    private String priceTerms;


    //  USD+汇总表体金额 (需要处理千分位)
    private String totalAmount;


    // 合同号 2024NGEQ/912608FR   (合同号)
    private String contractNo;

    // NYK OCEANUS   （船名航次）
    private String vessel;

    // 2025-02-27   （开航日期）
    private String goVesselDate;

    //   自  LE HAVRE（起运港-英文）
    private String portDeparture;


    // 当前日期 格式化  2025年02月18日
    private String currentDate;

}
