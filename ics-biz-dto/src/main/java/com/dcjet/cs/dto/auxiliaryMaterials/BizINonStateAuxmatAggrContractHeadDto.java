package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-6-18
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizINonStateAuxmatAggrContractHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String id;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 买方
      */
    @ApiModelProperty("买方")
	private  String buyer;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String supplier;
	/**
      * 签约日期
      */
    @ApiModelProperty("签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date signingDate;
	/**
      * 国内委托方
      */
    @ApiModelProperty("国内委托方")
	private  String domesticPrincipal;
	/**
      * 运输方式
      */
    @ApiModelProperty("运输方式")
	private  String transportMode;
	/**
      * 合同生效期
      */
    @ApiModelProperty("合同生效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date effectiveDate;
	/**
      * 合同有效期
      */
    @ApiModelProperty("合同有效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date expiryDate;
	/**
      * 签约地点(中文)
      */
    @ApiModelProperty("签约地点(中文)")
	private  String signingLocationCn;
	/**
      * 签约地点(英文)
      */
    @ApiModelProperty("签约地点(英文)")
	private  String signingLocationEn;
	/**
      * 装运港
      */
    @ApiModelProperty("装运港")
	private  String portOfLoading;
	/**
      * 目的港
      */
    @ApiModelProperty("目的港")
	private  String portOfDestination;
	/**
      * 报关口岸
      */
    @ApiModelProperty("报关口岸")
	private  String customsPort;
	/**
      * 付款方式
      */
    @ApiModelProperty("付款方式")
	private  String paymentMethod;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String currency;
	/**
      * 价格条款
      */
    @ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
      * 价格条款对应港口
      */
    @ApiModelProperty("价格条款对应港口")
	private  String priceTermPort;
	/**
      * 建议授权签约人
      */
    @ApiModelProperty("建议授权签约人")
	private  String suggestedSigner;
	/**
      * 短溢数%
      */
    @ApiModelProperty("短溢数%")
	private  BigDecimal shortageOverflowPercent;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String remarks;
	/**
      * 版本号
      */
    @ApiModelProperty("版本号")
	private  String versionNo;
	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String status;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 审批状态
      */
    @ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String parentId;
	/**
      * 制单人
      */
    @ApiModelProperty("制单人")
	private  String createBy;
	/**
      * 制单日期
      */
    @ApiModelProperty("制单日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateBy;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 插入用户名
      */
    @ApiModelProperty("插入用户名")
	private  String insertUserName;
	/**
      * 更新用户名
      */
    @ApiModelProperty("更新用户名")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	/**
      * 划款金额
      */
    @ApiModelProperty("划款金额")
	private  BigDecimal paymentAmount;
	/**
      * 合同金额
      */
    @ApiModelProperty("合同金额")
	private  BigDecimal contractAmount;
	/**
      * 汇率
      */
    @ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
      * 关税率%
      */
    @ApiModelProperty("关税率%")
	private  BigDecimal tariffRate;
	/**
      * 关税金额
      */
    @ApiModelProperty("关税金额")
	private  BigDecimal tariffAmount;
	/**
      * 消费税率
      */
    @ApiModelProperty("消费税率")
	private  BigDecimal consumptionTaxRate;
	/**
      * 消费税金额
      */
    @ApiModelProperty("消费税金额")
	private  BigDecimal consumptionTaxAmount;
	/**
      * 增值税率%
      */
    @ApiModelProperty("增值税率%")
	private  BigDecimal vatRate;
	/**
      * 增值税金额
      */
    @ApiModelProperty("增值税金额")
	private  BigDecimal vatAmount;
	/**
      * 进出口公司代理费率%
      */
    @ApiModelProperty("进出口公司代理费率%")
	private  BigDecimal importExportAgencyRate;
	/**
      * 进出口公司代理费
      */
    @ApiModelProperty("进出口公司代理费")
	private  BigDecimal importExportAgencyFee;
	/**
      * 总公司代理费率%
      */
    @ApiModelProperty("总公司代理费率%")
	private  BigDecimal headquartersAgencyRate;
	/**
      * 总公司代理费
      */
    @ApiModelProperty("总公司代理费")
	private  BigDecimal headquartersAgencyFee;
	/**
      * 合同数量
      */
    @ApiModelProperty("合同数量")
	private  BigDecimal contractQuantity;
	/**
      * 计费重量（箱）
      */
    @ApiModelProperty("计费重量（箱）")
	private  BigDecimal billingWeight;
	/**
      * 通关费
      */
    @ApiModelProperty("通关费")
	private  BigDecimal customsClearanceFee;
	/**
      * 验柜服务费
      */
    @ApiModelProperty("验柜服务费")
	private  BigDecimal containerInspectionFee;
	/**
      * 货代费用
      */
    @ApiModelProperty("货代费用")
	private  BigDecimal freightForwarderFee;
	/**
      * 保险费率
      */
    @ApiModelProperty("保险费率")
	private  BigDecimal insuranceRate;
	/**
      * 保险费
      */
    @ApiModelProperty("保险费")
	private  BigDecimal insuranceFee;
	/**
      * 协议编号
      */
    @ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
      * 签约日期
      */
    @ApiModelProperty("签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date agreementSigningDate;
	/**
      * 代理费率
      */
    @ApiModelProperty("代理费率")
	private  BigDecimal agreementAgentFeeRate;
	/**
      * 建议授权签约人
      */
    @ApiModelProperty("建议授权签约人")
	private  String agreementSuggestedSigner;
	/**
      * 协议条款
      */
    @ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
      * 协议备注
      */
    @ApiModelProperty("协议备注")
	private  String agreementRemarks;

	/**
	 * 是否划款通知保存过(0否;1是)
	 */
	@ApiModelProperty("是否划款通知保存过(0否;1是)")
	private String isTransferNotice;

	private  BigDecimal decTotalToList;
	private  BigDecimal qtyToList;

	private  String unitToList;
	private  String merchandiseCategoriesToList;

	private String createtUserName;
	private Date insertTime;
	private BigDecimal totalAmount;

	private String hasHeadNotice;
	private String isCopy;
}
