package com.dcjet.cs.config.api.payment;

import com.dcjet.cs.common.service.ExcelService;

import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.params.RegistrationListDto;
import com.dcjet.cs.dto.params.RegistrationListParam;


import com.dcjet.cs.payment.service.RegistrationListService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@RestController
@RequestMapping("v1/registrationList")
@Api(tags = "接口")
public class RegistrationListController extends BaseController {
    @Resource
    private RegistrationListService registrationListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param registrationListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<RegistrationListDto>> getListPaged(@RequestBody RegistrationListParam registrationListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<RegistrationListDto>> paged = registrationListService.getListPaged(registrationListParam, pageParam);
        return paged;
    }
    /**
     * @param registrationListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<RegistrationListDto> insert(@Valid @RequestBody RegistrationListParam registrationListParam, UserInfoToken userInfo) {
        ResultObject<RegistrationListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        RegistrationListDto registrationListDto = registrationListService.insert(registrationListParam, userInfo);
        if (registrationListDto != null) {
            resultObject.setData(registrationListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param registrationListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<RegistrationListDto> update(@PathVariable String sid, @Valid @RequestBody RegistrationListParam registrationListParam, UserInfoToken userInfo) {
        registrationListParam.setSid(sid);
        RegistrationListDto registrationListDto = registrationListService.update(registrationListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (registrationListDto != null) {
            resultObject.setData(registrationListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 registrationListService.delete(sids, userInfo);
        return resultObject;
    }

    @ApiOperation("汇总表体合同数量及总金额")
    @PostMapping("getContractTotal")
    public ResultObject<RegistrationListDto> getContractTotal(@RequestBody RegistrationListParam registrationListParam, UserInfoToken userInfo) {
        return  registrationListService.getContractTotal(registrationListParam, userInfo);
    }


    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<RegistrationListParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<RegistrationListDto> registrationListDtos = registrationListService.selectAll(exportParam.getExportColumns(), userInfo);
        registrationListDtos = convertForPrint(registrationListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), registrationListDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<RegistrationListDto> convertForPrint(List<RegistrationListDto> list) {
        for (RegistrationListDto item : list) {

        }
        return list;
    }
}
