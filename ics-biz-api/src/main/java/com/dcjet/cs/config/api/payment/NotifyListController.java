package com.dcjet.cs.config.api.payment;


import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.CostIContractParam;
import com.dcjet.cs.dto.bi.CostIShippingOrderParam;
import com.dcjet.cs.dto.payment.NotifyListDto;
import com.dcjet.cs.dto.payment.NotifyListParam;
import com.dcjet.cs.payment.model.NotifyHead;
import com.dcjet.cs.payment.service.NotifyListService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/notifyList")
@Api(tags = "基础管理-企业基础信息接口")
public class NotifyListController extends BaseController {
    @Resource
    private NotifyListService notifyListService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param notifyListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    public ResultObject<List<NotifyListDto>> getListPaged(@RequestBody NotifyListParam notifyListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<NotifyListDto>> paged = notifyListService.getListPaged(notifyListParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<NotifyHead> insert(@Valid @RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject<NotifyHead> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        bizIAuxmatForContractHeadParam.setTradeCode(userInfo.getCompany());
        NotifyHead notifyHead = notifyListService.insert(bizIAuxmatForContractHeadParam, userInfo);
        resultObject.setData(notifyHead);
        return resultObject;
    }
    /**
     * @param costIContractParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping("insert1")
    public ResultObject<NotifyHead> insert1(@Valid @RequestBody CostIContractParam costIContractParam, UserInfoToken userInfo) {
        ResultObject<NotifyHead> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        costIContractParam.setTradeCode(userInfo.getCompany());
        NotifyHead notifyHead = notifyListService.insert1(costIContractParam, userInfo);
        resultObject.setData(notifyHead);
        return resultObject;
    }



    /**
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping("insert6")
    public ResultObject<NotifyHead> insert6(@Valid @RequestBody BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        ResultObject<NotifyHead> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        bizINonStateAuxmatAggrContractHeadParam.setTradeCode(userInfo.getCompany());
        NotifyHead notifyHead = notifyListService.insert6(bizINonStateAuxmatAggrContractHeadParam, userInfo);
        resultObject.setData(notifyHead);
        return resultObject;
    }

    /**
     * @param costIShippingOrderParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping("insertOrder")
    public ResultObject<NotifyHead> insertOrder(@Valid @RequestBody CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {
        ResultObject<NotifyHead> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        costIShippingOrderParam.setTradeCode(userInfo.getCompany());
        NotifyHead notifyHead = notifyListService.insertOrder(costIShippingOrderParam, userInfo);
        resultObject.setData(notifyHead);
        return resultObject;
    }
    /**
     * @param sid
     * @param notifyListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<NotifyHead> update(@PathVariable String sid, @Valid @RequestBody NotifyListParam notifyListParam, UserInfoToken userInfo) {
        notifyListParam.setSid(sid);
        NotifyHead notifyHead = notifyListService.update(notifyListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (notifyHead != null) {
            resultObject.setData(notifyHead);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     *
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @PostMapping("delete")
    public ResultObject delete(@RequestBody NotifyListParam notifyListParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        NotifyHead notifyHead  = notifyListService.delete(notifyListParam.getSids(),notifyListParam.getHeadId(),userInfo);
        if (notifyHead != null) {
            resultObject.setData(notifyHead);
        }
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<NotifyListParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<NotifyListDto> notifyListDtos = notifyListService.selectAll(exportParam.getExportColumns(), userInfo);
        notifyListDtos = convertForPrint(notifyListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), notifyListDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<NotifyListDto> convertForPrint(List<NotifyListDto> list) {
        for (NotifyListDto item : list) {

        }
        return list;
    }





}
