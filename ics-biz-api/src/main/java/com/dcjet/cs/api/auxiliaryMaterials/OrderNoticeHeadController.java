package com.dcjet.cs.api.auxiliaryMaterials;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.auxiliaryMaterials.service.OrderNoticeHeadService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/v1/importAuxMat/orderNoticeHead")
@Api(tags = "国营贸易进口辅料-订货通知表头")
public class OrderNoticeHeadController extends BaseController {
    @Resource
    private OrderNoticeHeadService orderNoticeHeadService;
    @Resource
    private ExcelService excelService;

    /**
     * 获取分页列表
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param pageParam            分页参数
     * @param userInfo             用户信息
     * @return 订货通知表头分页列表
     */
    @ApiOperation("获取订货通知表头分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication("/tobacco/auxiliaryMaterials/orderNotice")
    public ResultObject<List<OrderNoticeHeadDto>> getListPaged(@RequestBody OrderNoticeHeadParam orderNoticeHeadParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.orderNoticeHeadService.getListPaged(orderNoticeHeadParam, pageParam, userInfo);
    }

    /**
     * 获取所有客户
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 响应数据
     */
    @PostMapping("/customers")
    public ResultObject<List<String>> getAllCustomers(@RequestBody OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        List<String> customers = this.orderNoticeHeadService.getAllCustomers(orderNoticeHeadParam, userInfo);
        return ResultObject.createInstance(true, "查询成功", customers);
    }

    /**
     * 导出订货通知表头列表
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 响应实体
     */
    @ApiOperation("导出订货通知表头列表")
    @PostMapping("/export")
    public ResponseEntity<?> export(@RequestBody BasicExportParam<OrderNoticeHeadParam> exportParam, UserInfoToken<?> userInfo) {
        List<OrderNoticeHeadDto> dtoList = this.orderNoticeHeadService.getAllList(exportParam.getExportColumns(), userInfo);
        try {
            return this.excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8)
                    , exportParam.getHeader(), dtoList);
        } catch (Exception e) {
            throw new ErrorException(500, "导出失败!");
        }
    }

    /**
     * 获取选择购销合同号信息
     *
     * @param userInfo 用户信息
     * @return 购销合同号选择列表
     */
    @ApiOperation("获取选择购销合同号信息")
    @GetMapping("/getSelectContractInfo")
    public ResultObject<List<OrderNoticeSelectBuyContractDto>> getSelectBuyContractInfo(UserInfoToken<?> userInfo) {
        List<OrderNoticeSelectBuyContractDto> selectDtoList = this.orderNoticeHeadService.getSelectBuyContractInfo(userInfo);
        return ResultObject.createInstance(selectDtoList, selectDtoList.size());
    }

    /**
     * 获取新增所需数据
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 新增所需数据
     */
    @ApiOperation("获取新增所需数据")
    @PostMapping("/getAddRequiredData")
    public ResultObject<OrderNoticeAddDto> getAddRequiredData(@RequestBody OrderNoticeAddParam addParam
            , UserInfoToken<?> userInfo) {
        OrderNoticeAddDto addDto = this.orderNoticeHeadService.getSelectedAddRequiredData(addParam, userInfo);
        return ResultObject.createInstance(addDto, 1);
    }

    /**
     * 新增订货通知
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("新增订货通知表头")
    @PostMapping
    public ResultObject<?> insert(@Valid @RequestBody OrderNoticeAddParam addParam, UserInfoToken<?> userInfo) {
        OrderNoticeHeadDto headDto = this.orderNoticeHeadService.insert(addParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, headDto);
    }

    /**
     * 获取新增明细数据
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 新增明细数据
     */
    @ApiOperation("获取新增明细数据")
    @PostMapping("/getAddDetailData")
    public ResultObject<List<OrderNoticeListDto>> getAddListDetailData(@RequestBody OrderNoticeAddParam addParam, UserInfoToken<?> userInfo) {
        List<OrderNoticeListDto> dtoList = this.orderNoticeHeadService.getAddListDetailData(addParam, userInfo);
        return ResultObject.createInstance(true, "查询成功", dtoList);
    }

    /**
     * 新增明细
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("新增明细")
    @PostMapping("/addDetail")
    public ResultObject<?> addListDetail(@RequestBody OrderNoticeAddParam addParam, UserInfoToken<?> userInfo) {
        this.orderNoticeHeadService.addListDetail(addParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
    }

    /**
     * 修改订货通知表头
     *
     * @param id                   主键
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头传输模型
     */
    @ApiOperation("修改订货通知表头")
    @PutMapping("/{id}")
    public ResultObject<?> update(@PathVariable String id, @Valid @RequestBody OrderNoticeHeadParam orderNoticeHeadParam
            , UserInfoToken<?> userInfo) {
        orderNoticeHeadParam.setId(id);
        OrderNoticeHeadDto orderNoticeHeadDto = this.orderNoticeHeadService.update(orderNoticeHeadParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, orderNoticeHeadDto);
    }

    /**
     * 删除订货通知
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除订货通知")
    @DeleteMapping("/{ids}")
    public ResultObject<?> delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        this.orderNoticeHeadService.delete(ids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }

    /**
     * 订货通知确认
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("订货通知确认")
    @PutMapping("/confirm/{id}")
    public ResultObject<?> confirm(@PathVariable String id, UserInfoToken<?> userInfo) {
        OrderNoticeHeadDto orderNoticeHeadDto = this.orderNoticeHeadService.confirm(id, userInfo);
        return ResultObject.createInstance(true, "确认成功", orderNoticeHeadDto);
    }

    /**
     * 订货通知作废
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("订货通知作废")
    @PutMapping("/invalidate/{id}")
    public ResultObject<?> invalidate(@PathVariable String id, UserInfoToken<?> userInfo) {
        this.orderNoticeHeadService.invalidate(id, userInfo);
        return ResultObject.createInstance(true, "作废成功");
    }

    /**
     * 校验版本复制
     *
     * @param orderNo  订货编号
     * @param userInfo 用户信息
     * @return 响应数据，data为1则表示存在有效数据
     */
    @ApiOperation("校验版本复制")
    @GetMapping("/checkVersionCopy/{orderNo}")
    public ResultObject<String> checkVersionCopy(@PathVariable String orderNo, UserInfoToken<?> userInfo) {
        String hasValid = this.orderNoticeHeadService.checkVersionCopy(orderNo, userInfo);
        return ResultObject.createInstance(true, "校验成功", hasValid);
    }

    /**
     * 版本复制
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 响应数据
     */
    @ApiOperation("版本复制")
    @PostMapping("/versionCopy")
    public ResultObject<?> versionCopy(@RequestBody OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        this.orderNoticeHeadService.versionCopy(orderNoticeHeadParam, userInfo);
        return ResultObject.createInstance(true, "版本复制成功");
    }

    /**
     * 打印采购订单表
     *
     * @param printParam 打印参数
     * @return 采购订单表文件数据
     */
    @ApiOperation("打印采购订单表")
    @PostMapping("/printOrderForm")
    public ResponseEntity<byte[]> printOrderForm(@RequestBody PurchaseOrderFormParam printParam, UserInfoToken<?> userInfo) {
        try {
            List<String> ids = printParam.getContractIds();
            String type = printParam.getType();
            boolean isPdf = "PDF".equals(type), isSingle = CollectionUtils.isNotEmpty(ids) && ids.size() == 1;
            byte[] fileBytes = this.orderNoticeHeadService.printOrderForm(ids, type, userInfo);
            String outName = "采购订单表." + (isSingle ? (isPdf ? "pdf" : "xlsx") : "zip");
            HttpHeaders h = new HttpHeaders();
            h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
            h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(fileBytes, h, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException("打印采购订单表错误", e);
        }
    }
}