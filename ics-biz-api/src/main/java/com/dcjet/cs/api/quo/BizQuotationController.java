package com.dcjet.cs.api.quo;
import com.dcjet.cs.bi.service.BizMaterialInformationService;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.bi.BizMaterialInformationDto;
import com.dcjet.cs.dto.bi.BizMaterialInformationParam;
import com.dcjet.cs.dto.bi.BizMerchantDto;
import com.dcjet.cs.dto.bi.BizMerchantParam;
import com.dcjet.cs.dto.quo.BizQuotationDto;
import com.dcjet.cs.dto.quo.BizQuotationParam;
import com.dcjet.cs.dto.quo.BizQuotationExportParam;
import com.dcjet.cs.quo.service.BizQuotationService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.Optional;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-20
 */
@RestController
@RequestMapping("v1/bizQuotation")
@Api(tags = "接口")
public class BizQuotationController extends BaseController {
    @Resource
    private BizQuotationService bizQuotationService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantService bizMerchantService;
    @Resource
    private BizMaterialInformationService bizMaterialInformationService;
    /**
     * @param bizQuotationParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizQuotationDto>> getListPaged(@RequestBody BizQuotationParam bizQuotationParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizQuotationDto>> paged = bizQuotationService.getListPaged(bizQuotationParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizQuotationDto> insert(@Valid @RequestBody BizQuotationParam bizQuotationParam, UserInfoToken userInfo) {
        ResultObject<BizQuotationDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizQuotationDto bizQuotationDto = bizQuotationService.insert(bizQuotationParam, userInfo);
        if (bizQuotationDto != null) {
            resultObject.setData(bizQuotationDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizQuotationDto> update(@PathVariable String sid, @Valid @RequestBody BizQuotationParam bizQuotationParam, UserInfoToken userInfo) {
        bizQuotationParam.setSid(sid);
        BizQuotationDto bizQuotationDto = bizQuotationService.update(bizQuotationParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizQuotationDto != null) {
            resultObject.setData(bizQuotationDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizQuotationService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizQuotationExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizQuotationDto> bizQuotationDtos = bizQuotationService.selectAll(exportParam.getExportColumns(), userInfo);
        List<BizMerchantDto> bizMerchants = bizMerchantService.selectAll(new BizMerchantParam(),userInfo);
        convertForPrint(bizQuotationDtos,bizMerchants);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizQuotationDtos);
    }
    @ApiOperation("作废接口")
    @PostMapping("/cancel/{sids}")
    public ResultObject<BizQuotationDto> cancel(@PathVariable List<String> sids, UserInfoToken userInfo) {
        Boolean dto = bizQuotationService.enable(sids,"1", userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (dto == false) {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    @ApiOperation("启用接口")
    @PostMapping("/enable/{sids}")
    public ResultObject<BizQuotationDto> enable(@PathVariable List<String> sids, UserInfoToken userInfo) {
        Boolean dto = bizQuotationService.enable(sids,"0", userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (dto == false) {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizQuotationDto> list, List<BizMerchantDto> bizMerchants) {
        for(BizQuotationDto item : list) {
            if (StringUtils.isNotBlank(item.getBusinessType())){
                item.setBusinessType(item.getBusinessType() + " " + CommonEnum.COMMON_BUSINESS2_TYPE_ENUM.getValue(item.getBusinessType()));
            }
            if (StringUtils.isNotBlank(item.getStatus())){
                item.setStatus(item.getStatus() + " " + CommonEnum.DATA_STATUS_ENUM.getValue(item.getStatus()));
            }
            if (StringUtils.isNotBlank(item.getMerchantCode())){
                Optional<BizMerchantDto> first = bizMerchants.stream().filter(x -> x.getMerchantCode().equals(item.getMerchantCode())).findFirst();
                first.ifPresent(bizMerchantDto -> item.setMerchantCode(item.getMerchantCode() + " " + bizMerchantDto.getMerchantNameCn()));
            }
        }
    }

    @ApiOperation("获取商品名称")
    @PostMapping(value = "/getGNameList/{commonMark}")
    public ResultObject getGNameList(@PathVariable String commonMark, UserInfoToken userInfo) throws Exception{
        ResultObject resultObject = ResultObject.createInstance(true);
        BizMaterialInformationParam param = new BizMaterialInformationParam();
        param.setCommonMark(commonMark);
        param.setDataState("0");
        List<BizMaterialInformationDto> bizMaterialInformationDtos = bizMaterialInformationService.selectAll(param, userInfo);
        resultObject.setData(bizMaterialInformationDtos);
        return resultObject;
    }

    /**
     * 根据交易代码获取物料信息(用于购销合同)
     * @param userInfo 用户信息
     * @return 物料信息列表
     */
    @ApiOperation("根据交易代码获取物料信息(用于购销合同)")
    @PostMapping("/matForBuyContract")
    public ResultObject<List<BizQuotationDto>> getMatForBuyContract(@RequestBody BizQuotationDto mat,UserInfoToken userInfo ) {
        ResultObject<List<BizQuotationDto>> resultObject = ResultObject.createInstance(true);
        List<BizQuotationDto> materialList = bizQuotationService.getMatForBuyContract(mat,userInfo);
        resultObject.setData(materialList);
        return resultObject;
    }

    @ApiOperation("打印接装纸格式报价表")
    @PostMapping("printTippingPaper")
    public ResponseEntity printTippingPaper(@RequestBody BizQuotationParam bizQuotationParam, UserInfoToken userInfo) throws Exception {
        return bizQuotationService.printTippingPaper(bizQuotationParam, userInfo);
    }
    @ApiOperation("打印卷烟纸格式报价表")
    @PostMapping("printCigarettePaper")
    public ResponseEntity printCigarettePaper(@RequestBody BizQuotationParam bizQuotationParam, UserInfoToken userInfo) throws Exception {
        return bizQuotationService.printCigarettePaper(bizQuotationParam, userInfo);
    }
}
