package com.dcjet.cs.api.warehouse;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadDto;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadParam;
import com.dcjet.cs.dto.warehouse.*;
import com.dcjet.cs.warehouse.dao.BizStoreEHeadMapper;
import com.dcjet.cs.warehouse.dao.BizStoreEListMapper;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import com.dcjet.cs.warehouse.model.BizStoreEList;
import com.dcjet.cs.warehouse.service.BizStoreEHeadService;
import com.xdo.common.exception.ErrorException;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * generated by Generate dc
 *
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@RestController
@RequestMapping("v1/bizStoreEHead")
@Api(tags = "接口")
public class BizStoreEHeadController extends BaseController {
    @Resource
    private BizStoreEHeadService bizStoreEHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizStoreEHeadMapper bizStoreEHeadMapper;

    @Resource
    private ExportService exportService;
    /**
     * @param bizStoreEHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizStoreEHeadDto>> getListPaged(@RequestBody BizStoreEHeadParam bizStoreEHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizStoreEHeadDto>> paged = bizStoreEHeadService.getListPaged(bizStoreEHeadParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizStoreEHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizStoreEHeadDto> insert(@Valid @RequestBody BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        ResultObject<BizStoreEHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizStoreEHeadDto bizStoreEHeadDto = bizStoreEHeadService.insert(bizStoreEHeadParam, userInfo);
        if (bizStoreEHeadDto != null) {
            resultObject.setData(bizStoreEHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizStoreEHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizStoreEHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        bizStoreEHeadParam.setSid(sid);
        BizStoreEHeadDto bizStoreEHeadDto = bizStoreEHeadService.update(bizStoreEHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizStoreEHeadDto != null) {
            resultObject.setData(bizStoreEHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizStoreEHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizStoreEHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizStoreEHeadDto> bizStoreEHeadDtos = bizStoreEHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizStoreEHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizStoreEHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizStoreEHeadDto> list) {
        for(BizStoreEHeadDto item : list) {
        }
    }
    @PostMapping("/getListBySid/{sid}")
    public ResultObject getListBySid(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreEHeadService.getListBySid(sid,userInfo);
    }
    @ApiOperation("获取根据head_sid")
    @PostMapping("/getStoreEHeadByHeadSid")
    public ResultObject <BizStoreEHeadDto> getStoreEHeadByHeadSid(@RequestBody BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        return bizStoreEHeadService.getStoreEHeadByHeadSid(bizStoreEHeadParam, userInfo);
    }

    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        return bizStoreEHeadService.confirmStatus(bizStoreEHeadParam, userInfo);
    }
    @ApiOperation("红冲接口")
    @PostMapping("redFlush/{sid}")
    public ResultObject redFlush(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreEHeadService.redFlush(sid, userInfo);
    }

    @ApiOperation("打印出库回单")
    @PostMapping("/printOutBoundReceipt/{sid}/{sType}")
    public ResponseEntity printOutBoundReceipt(@PathVariable String sid, @PathVariable String sType, UserInfoToken userInfo) throws IOException {
        BizStoreEHead storeEHeadByHeadSid = bizStoreEHeadMapper.getStoreEHeadByHeadSid(sid);
        if(storeEHeadByHeadSid == null){
            throw new ErrorException(400,"未生成出库回单！");
        }
        String tempName = "out_bound_receipt.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());

        String outName = xdoi18n.XdoI18nUtil.t("出库回单")+formattedDate + ".pdf";
        String fileName = UUID.randomUUID().toString() +  ".xlsx";
        if ("excel".equals(sType)) {
            outName = xdoi18n.XdoI18nUtil.t("出库回单")+formattedDate + ".xlsx";
        }
        ConversionPrintOutBoundReceiptHead conversionMessageHead = bizStoreEHeadService.conversionMessageHead(storeEHeadByHeadSid,userInfo);
        List<BizStoreEList> bizStoreELists = bizStoreEHeadService.conversionMessageList(conversionMessageHead,storeEHeadByHeadSid,userInfo);
        String exportFileName = exportService.export(Arrays.asList(conversionMessageHead),bizStoreELists, fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if("pdf".equals(sType)) {
            fileBytes = ExportService.excelToPdf(fileBytes);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
}
