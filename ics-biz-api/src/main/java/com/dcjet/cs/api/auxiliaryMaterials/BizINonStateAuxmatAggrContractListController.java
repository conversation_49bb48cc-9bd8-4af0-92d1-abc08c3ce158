package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractListParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractListExportParam;
import com.dcjet.cs.auxiliaryMaterials.service.BizINonStateAuxmatAggrContractListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@RestController
@RequestMapping("v1/bizINonStateAuxmatAggrContractList")
@Api(tags = "接口")
public class BizINonStateAuxmatAggrContractListController extends BaseController {
    @Resource
    private BizINonStateAuxmatAggrContractListService bizINonStateAuxmatAggrContractListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizINonStateAuxmatAggrContractListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizINonStateAuxmatAggrContractListDto>> getListPaged(@RequestBody BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizINonStateAuxmatAggrContractListDto>> paged = bizINonStateAuxmatAggrContractListService.getListPaged(bizINonStateAuxmatAggrContractListParam, pageParam);
        return paged;
    }
    /**
     * @param bizINonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizINonStateAuxmatAggrContractListDto> insert(@Valid @RequestBody BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        ResultObject<BizINonStateAuxmatAggrContractListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizINonStateAuxmatAggrContractListDto bizINonStateAuxmatAggrContractListDto = bizINonStateAuxmatAggrContractListService.insert(bizINonStateAuxmatAggrContractListParam, userInfo);
        if (bizINonStateAuxmatAggrContractListDto != null) {
            resultObject.setData(bizINonStateAuxmatAggrContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizINonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizINonStateAuxmatAggrContractListDto> update(@PathVariable String sid, @Valid @RequestBody BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        bizINonStateAuxmatAggrContractListParam.setId(sid);
        BizINonStateAuxmatAggrContractListDto bizINonStateAuxmatAggrContractListDto = bizINonStateAuxmatAggrContractListService.update(bizINonStateAuxmatAggrContractListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizINonStateAuxmatAggrContractListDto != null) {
            resultObject.setData(bizINonStateAuxmatAggrContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizINonStateAuxmatAggrContractListService.delete(sids, userInfo);
        return resultObject;
    }
}
