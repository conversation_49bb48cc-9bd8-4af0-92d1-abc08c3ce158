package com.dcjet.cs.api.customerAccount;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountDto;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountParam;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountExportParam;
import com.dcjet.cs.customerAccount.service.BizCustomerAccountService;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanParam;
import com.dcjet.cs.dto.warehouse.BizStoreIHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-6-16
 */
@RestController
@RequestMapping("v1/customerAccount")
@Api(tags = "接口")
public class BizCustomerAccountController extends BaseController {
    @Resource
    private BizCustomerAccountService bizCustomerAccountService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    /**
     * @param bizCustomerAccountParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizCustomerAccountDto>> getListPaged(@RequestBody BizCustomerAccountParam bizCustomerAccountParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizCustomerAccountDto>> paged = bizCustomerAccountService.getListPaged(bizCustomerAccountParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizCustomerAccountParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizCustomerAccountDto> insert(@Valid @RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        ResultObject<BizCustomerAccountDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizCustomerAccountDto bizCustomerAccountDto = bizCustomerAccountService.insert(bizCustomerAccountParam, userInfo);
        if (bizCustomerAccountDto != null) {
            resultObject.setData(bizCustomerAccountDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizCustomerAccountParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizCustomerAccountDto> update(@PathVariable String sid, @Valid @RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        bizCustomerAccountParam.setSid(sid);
        BizCustomerAccountDto bizCustomerAccountDto = bizCustomerAccountService.update(bizCustomerAccountParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizCustomerAccountDto != null) {
            resultObject.setData(bizCustomerAccountDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizCustomerAccountService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizCustomerAccountExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizCustomerAccountDto> bizCustomerAccountDtos = bizCustomerAccountService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizCustomerAccountDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizCustomerAccountDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizCustomerAccountDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);

        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);
        for(BizCustomerAccountDto item : list) {
            item.setBusinessType(item.getBusinessType() + " " + CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(item.getBusinessType()));
            String customerNameCn = getMerchantNameSafely(bizMerchantMap, item.getCustomer());
            item.setCustomer(item.getCustomer() + " " + customerNameCn);
            item.setCurr(pCodeHolder.getValue(PCodeType.CURR, item.getCurr()));
            if(StringUtils.isNotBlank(item.getSendFinance())){
                if("0".equals(item.getSendFinance())){
                    item.setSendFinance("0 是");
                }
                if("1".equals(item.getSendFinance())){
                    item.setSendFinance("1 否");
                }
            }
            item.setStatus(CommonEnum.OrderStatusEnum.getValue(item.getStatus()));
        }
    }
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountService.sendApproval(sid, userInfo);
    }
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        return bizCustomerAccountService.confirmStatus(bizCustomerAccountParam, userInfo);
    }
    @ApiOperation("退单接口")
    @PostMapping("back/{sid}")
    public ResultObject back(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountService.back(sid, userInfo);
    }
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountService.invalidate(sid, userInfo);
    }
    @ApiOperation("红冲接口")
    @PostMapping("redFlush/{sid}")
    public ResultObject redFlush(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountService.redFlush(sid, userInfo);
    }

    @ApiOperation("校验是否存在 同一个订单号是否存在未作废的数据")
    @PostMapping("/checkIdNotCancel")
    public ResultObject checkPlanIdNotCancel(@RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        return bizCustomerAccountService.checkIdNotCancel(bizCustomerAccountParam,userInfo);
    }
    @ApiOperation("版本复制")
    @PostMapping("/copyVersion")
    public ResultObject copyVersion(@RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        return bizCustomerAccountService.copyVersion(bizCustomerAccountParam,userInfo);
    }

    @ApiOperation("打印入库回单")
    @PostMapping("/print")
    public ResponseEntity print(@RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) throws Exception {
        return bizCustomerAccountService.print(bizCustomerAccountParam, userInfo);
    }
    @ApiOperation("从合同新增")
    @PostMapping("insertByShipping")
    public ResultObject insertByShipping(@RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        return bizCustomerAccountService.insertByShipping(bizCustomerAccountParam, userInfo);
    }
    @ApiOperation("从进货明细新增")
    @PostMapping("insertByContract")
    public ResultObject insertByContract(@RequestBody BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        return bizCustomerAccountService.insertByContract(bizCustomerAccountParam, userInfo);
    }

}
